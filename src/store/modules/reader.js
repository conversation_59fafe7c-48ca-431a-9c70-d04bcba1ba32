/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-19 08:47:46
 * @LastEditTime: 2025-02-20 19:32:52
 * @FilePath: \dutp-stu-tea-vue\src\store\modules\reader.js
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { nextTick } from 'vue'
import { getInfoByChapterId as getChapterTemplateInfo } from '@/api/book/reader'
import {
  PAGE_ITEMS_CSS_CLASS,
  PAGE_ITEMS_CONTAINER_ID,
  PAGE_TURNING_ERROR_MAPPER,
  getChapterContent,
  getChapterContentInSimple
} from '@/utils/reader'
import { ElMessage } from 'element-plus'
import { BOOK_MASTER_FLAG } from '@/utils/reader'
import { TEXT_ID_DELIMITER } from '@/utils/reader'
import { getSectionIdName } from '@/utils/index.js'

export const READING_MODE = {
  // 当前阅读模式 reading | absorbed | automaticReading
  AUTO_READING: 'automaticReading',
  ABSORBED: 'absorbed',
  GENERAL: 'reading'
}

function findCatalogDataBy(catalogsList, predictFn, callbackFn) {
  for (let i = 0, len = catalogsList.length; i < len; i++) {
    if (predictFn(catalogsList[i])) {
      callbackFn(catalogsList[i])
      return
    }
    // 递归查找子级目录
    if (catalogsList[i].children && catalogsList[i].children.length > 0) {
      findCatalogDataBy(catalogsList[i].children, predictFn, callbackFn)
    }
  }
}

function getChapterDataByPageIndexInBook(store, pageIndexInBook) {
  // 递归收集所有有内容的章节，按照sort字段排序
  function collectContentChapters(chapters) {
    let contentChapters = [];

    // 先按sort字段排序
    const sortedChapters = [...chapters].sort((a, b) => (a.sort || 0) - (b.sort || 0));

    for (let i = 0; i < sortedChapters.length; i++) {
      const chapter = sortedChapters[i];

      // 如果有子章节，递归处理
      if (chapter.children && chapter.children.length > 0) {
        contentChapters = contentChapters.concat(collectContentChapters(chapter.children));
      } else if (chapter.totalPages > 0) {
        // 只收集有实际页面内容的章节（最底层且有页面的章节）
        contentChapters.push(chapter);
      }
    }
    return contentChapters;
  }

  const contentChapters = collectContentChapters(store.comprehensiveChapterAndCatalogData.chaptersData);
  console.log('有内容的章节列表:', contentChapters.map(c => ({ title: c.title, totalPages: c.totalPages, sort: c.sort })));

  let cumulatedPagesInTotal = 0;
  let chapterData = null;

  for (let i = 0, len = contentChapters.length; i < len; i++) {
    const currentChapterPages = contentChapters[i].totalPages || 0;
    if (cumulatedPagesInTotal + currentChapterPages >= pageIndexInBook) {
      chapterData = contentChapters[i];
      console.log(`页面 ${pageIndexInBook} 属于章节: ${chapterData.title}, 累计页数: ${cumulatedPagesInTotal}, 当前章节页数: ${currentChapterPages}`);
      break;
    }
    cumulatedPagesInTotal += currentChapterPages;
  }
  return chapterData;
}
function findNodeDataByNodeId(nodeList, predictFn, rootPageIndex, callbackFn) {
  for (let i = 0, len = nodeList.length; i < len; i++) {
    if (nodeList[i].type === 'page') {
      rootPageIndex = i
    }
    if (predictFn(nodeList[i])) {
      callbackFn(rootPageIndex)
      break
    }
    findNodeDataByNodeId(nodeList[i].content || [], predictFn, rootPageIndex, callbackFn)
  }
}
/**
 * @description 根据chapterId查找对应的节点, 该方法调用前需保证对应章节的内容已经加载
 * @param {*} store
 * @param {*} chapterId
 * @param {*} predictFn
 * @param {*} callbackFn
 */
function findPageDataByNodeIdAndChapterId(store, chapterId, predictFn, callbackFn) {
  const filteredPageDataArray = store.pageData.filter(pageDetail => pageDetail.chapterId === chapterId)
  findNodeDataByNodeId(filteredPageDataArray, predictFn, null, callbackFn)
}
/**
 * @description 计算出当前章节此前所有章节的页数总和
 */
function calculateTotalPagesBeforeChapter(store, targetChapterId) {
  // 递归收集所有有内容的章节，按照sort字段排序
  function collectContentChapters(chapters) {
    let contentChapters = [];

    // // 先按sort字段排序
    // const sortedChapters = [...chapters].sort((a, b) => (a.sort || 0) - (b.sort || 0));

    for (let i = 0; i < chapters.length; i++) {
      const chapter = chapters[i];

      // 如果有子章节，递归处理
      if (chapter.children && chapter.children.length > 0) {
        contentChapters = contentChapters.concat(collectContentChapters(chapter.children));
      } else if (chapter.totalPages > 0) {
        // 只收集有实际页面内容的章节（最底层且有页面的章节）
        contentChapters.push(chapter);
      }
    }
    return contentChapters;
  }

  const contentChapters = collectContentChapters(store.comprehensiveChapterAndCatalogData.chaptersData);
  let cumulatedPages = 0;

  for (let i = 0; i < contentChapters.length; i++) {
    const chapter = contentChapters[i];

    if (chapter.chapterId === targetChapterId) {
      console.log(`计算章节 ${chapter.title} 之前的页数: ${cumulatedPages}`);
      return cumulatedPages;
    }

    cumulatedPages += chapter.totalPages || 0;
  }

  console.log(`未找到章节 ${targetChapterId}, 返回默认值 0`);
  return 0;
}
/**
 * @description 获取当前章节之前的章节或者之后的章节
 * @param {*} store
 * @param {*} previousOrNext
 * @returns
 */
function getChapterBasedOnIndex(store, previousOrNext = '') {
  // 递归收集所有章节（包括子章节）
  function collectAllChapters(chapters) {
    let allChapters = [];
    for (let i = 0; i < chapters.length; i++) {
      const chapter = chapters[i];
      allChapters.push(chapter);

      if (chapter.children && chapter.children.length > 0) {
        allChapters = allChapters.concat(collectAllChapters(chapter.children));
      }
    }
    return allChapters;
  }

  const allChapters = collectAllChapters(store.comprehensiveChapterAndCatalogData.chaptersData);
  const currentChapterIndex = allChapters.findIndex(
      chapterInfo => chapterInfo.chapterId === store.chapterId
  );

  if (previousOrNext === 'next') {
    return allChapters[currentChapterIndex + 1];
  } else if (previousOrNext === 'previous') {
    return allChapters[currentChapterIndex - 1];
  }
  return null;
}
/**
 * @description 该方法根据目前设定的浏览模式，跳转到对应的页面，同时设置目标页面对应的chapterId，该方法调用前需保证对应章节数据已经加载
 * @param {*} store
 * @param {*} pageIndexInBook 该页面是全书对应的页码
 */
function jumpToPageIndexInBook(store, pageIndexInBookToJumpTo, animateOpts) {
  const targetChapterData = getChapterDataByPageIndexInBook(store, pageIndexInBookToJumpTo)
  const pagesCntBeforeChapter = calculateTotalPagesBeforeChapter(store, targetChapterData.chapterId)
  const pageIndexInChapterToJumpTo = pageIndexInBookToJumpTo - pagesCntBeforeChapter
  store.setCurrentPageIndex(pageIndexInBookToJumpTo)
  switch (store.pageFlippingMethod) {
    // case 'x':
    //   // this.currentData = this.pageData[num]
    //   break;
    case 'y':
      const pagesDom = document.querySelectorAll(`#${PAGE_ITEMS_CONTAINER_ID} .${PAGE_ITEMS_CSS_CLASS}`)
      nextTick(() => {
        if (pagesDom[pageIndexInChapterToJumpTo - 1]) {
          pagesDom[pageIndexInChapterToJumpTo - 1].scrollIntoView(animateOpts)
        }
      })
      break
    case 'r':
      if (store.flex === 'one') {
        const idName = `#${store.onePageBoxIdName}`
        // $(idName).turn('previous')
        nextTick(() => {
          setTimeout(() => {
            // turn方法可以接收previous和next作为参数，这里由于通用性，没有采用，直接传入页码
            $(idName).turn('page', pageIndexInChapterToJumpTo)
          }, 0)
        })
      }

      // else if (this.flex === 'two') {

      //   const idName = `#${this.twoPageBoxIdName}`
      //   $(idName).turn('previous')
      // }
      //  else if (this.flex === 'two') {
      //   const idName = `#${this.twoPageBoxIdName}`
      //   $(idName).turn('previous')
      // }
      break
    default:
      break
  }
}

function getChapterIndexInCatalog(store, chapterId) {
  // 递归查找章节索引
  function findChapterIndex(chapters, id, index = 0) {
    for (let i = 0; i < chapters.length; i++) {
      const chapter = chapters[i];

      if (chapter.chapterId === id) {
        return index;
      }
      index++; // 增加索引计数

      // 如果有子章节，递归处理
      if (chapter.children && chapter.children.length > 0) {
        const result = findChapterIndex(chapter.children, id, index);
        if (typeof result === 'number' && result !== -1) {
          return result;
        }
        // 更新索引计数
        function countChildren(children) {
          let count = 0;
          for (let j = 0; j < children.length; j++) {
            count++;
            if (children[j].children && children[j].children.length > 0) {
              count += countChildren(children[j].children);
            }
          }
          return count;
        }
        index += countChildren(chapter.children);
      }
    }
    return -1; // 未找到
  }

  return findChapterIndex(store.comprehensiveChapterAndCatalogData.chaptersData, chapterId);
}
const useReader = defineStore('reader', {
  state: () => ({
    simplifiedMode: false,
    relatedBooks: [],
    comprehensiveBookData: {
      //当前书籍id
      bookId: undefined,
      bookName: undefined,
      // highlightCatalogHeader: undefined,
      currentPageIndex: undefined,
      totalPages: 0,
      pageIndexInBook4FirstPageInChapter: undefined,

      lastReadingPageIndexInChapter: 0,
      lastTimeReadingFlippingMethod: undefined,
      lastReadingMode: undefined
    },
    // 章节和目录
    comprehensiveChapterAndCatalogData: {
      chaptersData: []
    },
    pageData: [],
    // count: 0,//当前页
    // pageNumber: 0, // 所有章节总页数
    // currentData: [],
    toolIsHide: false, //右侧工具栏是否隐藏
    menuIsHide: false, //左侧菜单是否隐藏
    activeIndex: '', //左侧菜单激活项的index
    reading: 'reading', //当前阅读模式 reading | absorbed | automaticReading
    // autoTask: autoTask,
    // 新增默认值，用于编辑器进入阅读器预览时，不调startread接口，默认样式都为默认
    styleSetting: {
      'font-family': '0',
      'font-size': '-1px',
      'line-height': -1
    }, //页面样式设置
    flex: 'one', //布局方式 one --单栏 | two --双栏
    theme: 'light', //light | dark
    themeBackground: '#fff', //页面背景,
    pageFlippingMethod: 'y', //翻页方式 x--横向翻页 | y--上下滑动 | r--真实翻页
    onePageBoxIdName: '__flipbookContainerOne__', //单栏真实翻页的容器的id
    twoPageBoxIdName: '__flipbookContainerTwo__', //双栏真实翻页的容器的id
    configId: undefined, // 配置ID
    chapterId: undefined, // 章节ID
    // bookId: undefined,//书籍id
    aiDialog: null, //触发ai功能弹窗的函数
    bookMarkData: [], //书签列表
    bookLineData: [], //划线列表
    noteParagraphMap: new Map(),
    noteList: [], //笔记列表
    readId: undefined, //阅读记录ID
    templateStyle: {}, //编辑器的模版配置
    videoComponentCount: 0, // 视频组件计数
    videoPlayTime: 0 //视频播放时间 单位:秒
  }),
  actions: {
    setSimplifiedMode(simplifiedMode) {
      this.simplifiedMode = simplifiedMode
    },
    findPrimaryBook() {
      return this.relatedBooks.find(book => book.masterFlag === BOOK_MASTER_FLAG.PRIMARY_BOOK)
    },
    setRelatedBooks(books) {
      this.relatedBooks = books
    },
    // async preloadChaptersContentData() {
    //   const chaptersData = this.comprehensiveChapterAndCatalogData.chaptersData;
    //   for (let i = 0, len = chaptersData.length; i < len; i++) {
    //     await this.loadChapterData(chaptersData[i].chapterId);
    //   }
    // },
    repairePageIndexInChapter(chapterId, pageIndexInChapter) {
      return new Promise((resolve, reject) => {
        const targetChapterData = this.getChapterCatalogDataByChapterId(chapterId)
        // 暂定：目录加载完成后才加载可以加载任何章节的内容
        if (!targetChapterData) {
          return reject(PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST)
        }
        pageIndexInChapter = Math.min(pageIndexInChapter, targetChapterData.totalPages)
        resolve(pageIndexInChapter)
      })
    },
    findCatalogDataById(catalogId, chapterId) {
      return new Promise(resolve => {
        const filteredCatalogData = this.comprehensiveChapterAndCatalogData.chaptersData.filter(
          catalogData => catalogData.chapterId === chapterId
        )
        findCatalogDataBy(
          filteredCatalogData,
          catalogItem => {
            return catalogId === catalogItem.catalogId
          },
          targetCatalogItem => {
            resolve(targetCatalogItem)
          }
        )
      })
    },
    setReadId(id) {
      this.readId = id
    },
    setCommonData(data) {
      // 测试数据s
      // data.lastPageNumber = 1
      // 搭建python环境章节
      // data.lastChapterId = '1892397631883571202';
      // 封面章节
      // data.lastChapterId = '1892758741593677826';
      // data.pageFlippingMethod = 'y'
      // data.reading = 'automaticReading'
      // data.readMode = 'reading'

      if (data.bgColor) {
        this.themeBackground = data.bgColor
      }
      const columnReadingMapper = {
        1: 'one',
        2: 'two'
      }
      this.flex = columnReadingMapper['' + data.columnQuantity] ?? 'one'

      if (data.theme) {
        this.theme = data.theme
      }

      this.comprehensiveBookData.bookId = data.bookId
      this.comprehensiveBookData.bookName = data.bookName
      // if (data.configId) {
      this.configId = data.configId || ''
      // }
      if (data.readMode) {
        this.comprehensiveBookData.lastReadingMode = data.readMode
      }
      // 这里这么判断是为了编辑器预览阅读器时，不赋值
      if (data.fontFamily) {
        this.styleSetting = {
          'font-family': data.fontFamily,
          'font-size': data.fontSize + 'px',
          'line-height': data.lineHeight
        }
      }

      if (data.lastChapterId) {
        this.comprehensiveBookData.lastReadingChapterId = data.lastChapterId
      }
      // 最后一次阅读的页面, 默认阅读第一页保证可以阅读, data.lastPageNumber表示章节对应的页码
      this.comprehensiveBookData.lastReadingPageIndexInChapter = data.lastPageNumber || 1
      // 默认阅读模式是y
      const flipMethodMapper = {
        r: 'r',
        y: 'y'
      }
      this.comprehensiveBookData.lastTimeReadingFlippingMethod = flipMethodMapper[data.pageFlippingMethod] ?? 'y'
    },
    setData(data, chapterId) {
      // this.currentData = []
      const chapterCatalogData = this.getChapterCatalogDataByChapterId(chapterId)
      this.pageData = data.content.map((pageData, pageIndexInChapter) => {
        return {
          ...pageData,
          attrs: {
            ...pageData.attrs,
            _pageNumberInChapter: pageIndexInChapter + 1
          },
          chapterId,
          hasAccessToChapter: chapterCatalogData?.hasAccessToChapter ?? false
        }
      })
    },
    // 跳转到上一页
    lastPage() {
      return new Promise((resolve, reject) => {
        if (this.comprehensiveBookData.currentPageIndex === 1) {
          // 当前是第一页时，无法点击上一页
          ElMessage.warning(PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.msg)
          return reject(PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK)
        }
        if (this.isFirstPageInChapter()) {
          const previousChapter = this.getPreviousChapter()
          if (previousChapter) {
            // 如果是当前章节第一页，需要加载上一章内容
            this.loadChapterData(previousChapter.chapterId)
              .then(() => {
                // 跳转到上一页
                jumpToPageIndexInBook(this, this.comprehensiveBookData.currentPageIndex - 1)
                resolve(null)
              })
              .catch(reject)
          } else {
            reject({ msg: '上一个章节不存在' })
          }
        } else {
          jumpToPageIndexInBook(this, this.comprehensiveBookData.currentPageIndex - 1)
          resolve(null)
        }
      })
    },
    // 跳转到下一页
    nextPage() {
      return new Promise((resolve, reject) => {
        if (this.comprehensiveBookData.currentPageIndex === this.comprehensiveBookData.totalPages) {
          // 当前是最后一页时，无法点击下一页
          ElMessage.warning(PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.msg)
          return reject(PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK)
        }
        if (this.isLastPageInChapter()) {
          const nextChapter = this.getNextChapter()
          if (nextChapter) {
            // 如果是当前章节最后一页，需要加载下一章内容
            this.loadChapterData(nextChapter.chapterId)
              .then(() => {
                // 跳转到下一页
                jumpToPageIndexInBook(this, this.comprehensiveBookData.currentPageIndex + 1)
                resolve(null)
              })
              .catch(reject)
          } else {
            reject({ msg: '下一个章节不存在' })
          }
        } else {
          jumpToPageIndexInBook(this, this.comprehensiveBookData.currentPageIndex + 1)
          resolve(null)
        }
      })
    },
    setReading(type) {
      this.reading = type
      if (type === 'absorbed') {
        this.openAbsorbed()
      } else {
        this.closeAbsorbed()
      }
    },

    /**
     * 打开专注模式
     * 1-左侧菜单隐藏
     * 2-右侧侧边栏隐藏
     * 3-头部右侧切换为退出专注模式的功能模块
     */
    openAbsorbed() {
      this.toolIsHide = true
      this.menuIsHide = true
    },
    closeAbsorbed() {
      this.toolIsHide = false
      this.menuIsHide = false
    },
    /**
     * 修改布局样式
     */
    setFlex(val) {
      this.flex = val
      if (val === 'one') {
        this.menuIsHide = false
      } else if (val === 'two') {
        if (this.pageFlippingMethod === 'y') this.setPageFlippingMethod('x')
        this.menuIsHide = true
      }
    },

    /**
     * 修改翻页方式
     */
    setPageFlippingMethod(val) {
      this.pageFlippingMethod = val
    },

    /**
     * 纵向翻页定位页面
     */
    // page_y_display_page(params = {}) {
    //   if (this.pageFlippingMethod === 'y') {
    //     nextTick(() => {
    //       const idName = 'page_' + (this.comprehensiveBookData.currentPageIndex - 1)
    //       const Dom = document.getElementById(idName)
    //       if (Dom) Dom.scrollIntoView(params)
    //       // {behavior: 'smooth',block: 'center',}
    //     })
    //   }
    // },
    /**
     * 修改划线数据
     * 生成styley样式
     */
    setBookLineData(v) {
      this.bookLineData = v

      let linemarkIdsList = []
      const STYLEID = '__line__style__'
      let style = document.getElementById(STYLEID)
      if (style) style.remove()
      style = document.createElement('style')
      style.id = STYLEID
      v.forEach(ele => {
        const chunkedFromWordIds = ele.fromWordId.split(TEXT_ID_DELIMITER)
        const chunkedEndWordIds = ele.endWordId.split(TEXT_ID_DELIMITER)
        for (let i = 0, len = Math.min(chunkedFromWordIds.length, chunkedEndWordIds.length); i < len; i++) {
          const idStr = getSectionIdName(chunkedFromWordIds[i], chunkedEndWordIds[i])
          linemarkIdsList = linemarkIdsList.concat(idStr.map(idStr => idStr.substring(1)))
          if (ele.lineStyle === 'background') {
            style.innerHTML += `${idStr}{background-color:${ele.color}}`
          } else {
            style.innerHTML += `${idStr}{text-decoration:${ele.lineStyle};text-decoration-color:${ele.color}}`
          }
        }
      })
      const Body = document.querySelector('body')
      Body.appendChild(style)
    },
    setNoteList(val) {
      this.noteList = [...val]

      // 生成段落id为key的数据，主要为了点击单个节点时，快速找到对应的noteId
      const paragraphIdMap = new Map()
      val.forEach(element => {
        const fromWordIds = element.fromWordId.split(TEXT_ID_DELIMITER)
        const endWordIds = element.endWordId.split(TEXT_ID_DELIMITER)
        for (let i = 0, len = Math.min(fromWordIds.length, endWordIds.length); i < len; i++) {
          const startNodeSplicedList = fromWordIds[i].split('_')
          const endNodeSplicedList = endWordIds[i].split('_')
          if (!startNodeSplicedList[0]) {
            continue
          }
          if (!paragraphIdMap.get(startNodeSplicedList[0])) {
            paragraphIdMap.set(startNodeSplicedList[0], [])
          }
          if (startNodeSplicedList[0] && startNodeSplicedList[1] && endNodeSplicedList[0] && endNodeSplicedList[1]) {
            let startIndex = window.parseInt(startNodeSplicedList[1])
            let endIndex = window.parseInt(endNodeSplicedList[1])
            paragraphIdMap.get(startNodeSplicedList[0]).push({
              noteId: element.noteId, // 笔记id
              start: startIndex,
              end: endIndex
            })
          }
        }
      })
      this.noteParagraphMap = paragraphIdMap
    },
    setTemplateStyle(val) {
      this.templateStyle = val
    },
    //修改菜单激活项
    setActiveIndex(val) {
      this.activeIndex = val
    },
    // 主要用于加载下一章内容,并设置当前chapterId,以及记录素材模板
    async loadChapterData(chapterId) {
      const targetChapterData = this.getChapterCatalogDataByChapterId(chapterId)
      // 暂定：目录加载完成后才加载可以加载任何章节的内容
      if (!targetChapterData) {
        return Promise.reject(PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST)
      }
      let sameChapter = false
      if (this.chapterId === chapterId) {
        sameChapter = true
      }
      const chapterContentPromise = new Promise((resolve, reject) => {
        if (targetChapterData?.chapterContent) {
          return resolve({
            code: 200,
            data: {
              fromCache: true,
              sameChapter,
              content: targetChapterData.chapterContent,
              chapterId: targetChapterData.chapterId,
              startPageNumber: 1
            },
            msg: ''
          })
        }
        let getChapterContentPromise = null

        if (this.simplifiedMode) {
          getChapterContentPromise = getChapterContentInSimple(this.comprehensiveBookData.bookId, chapterId, targetChapterData.chapterName)
        } else {
          getChapterContentPromise = getChapterContent(this.comprehensiveBookData.bookId, chapterId, targetChapterData.chapterName)
        }
        getChapterContentPromise
          .then(resp => {
            resp.data = {
              ...resp.data
            }
            resolve(resp)
          })
          .catch(resp => {
            reject(resp)
          })
      })
      return Promise.all([chapterContentPromise, getChapterTemplateInfo(chapterId)]).then(resp => {
        const [res1, res2] = resp
        if (res1.code === 200) {
          this.setChapterContent(chapterId, res1.data.content, res1.data.fromCache, res1.data.sameChapter)
        } else if (res1.code !== 200) {
          ElMessage.error(res1.msg)
        }
        if (res2.code === 200) this.setTemplateStyle(res2.data)
        // 设置当前左侧应高亮的章节菜单
        this.setActiveIndex(chapterId)
      })
    },
    deauthorizeToTheBook() {
      this.comprehensiveBookData.bookId = undefined
      this.comprehensiveChapterAndCatalogData.chaptersData = []
      this.readId = undefined
      this.setData({ content: [] })
      this.setCurrentChapterId(undefined)
      this.setCurrentPageIndex(undefined)
      this.comprehensiveBookData.lastReadingChapterId = undefined
      this.comprehensiveBookData.lastReadingPageIndexInChapter = undefined
      this.comprehensiveBookData.lastTimeReadingFlippingMethod = undefined
    },
    setCurrentChapterId(chapterId) {
      this.chapterId = chapterId
      const cumulatedPagesInTotal = calculateTotalPagesBeforeChapter(this, chapterId)
      this.comprehensiveBookData.pageIndexInBook4FirstPageInChapter = 1 + cumulatedPagesInTotal
    },
    // [{chapterId: '', totalPages:'', chapterContent: {}, free: ''}]
    setChapterContent(chapterId, chapterContentStr, fromCache, sameChapter) {
      const targetChapterData = this.getChapterCatalogDataByChapterId(chapterId)
      this.setCurrentChapterId(chapterId)
      try {
        if (!fromCache) {
          targetChapterData.chapterContent = JSON.parse(chapterContentStr)
        } else {
          targetChapterData.chapterContent = chapterContentStr
        }
        if (!sameChapter) {
          // 如果是同一章节，不重复设置数据
          this.setData(targetChapterData.chapterContent, chapterId)
        }
      } catch (err) {
        console.error(err)
        throw new Error('设置章节内容出错,该章节内容不是有效可以转换的文档内容')
      }
      return true
    },
    // 获取当前章节此前所有章节一共多少页
    calculateTotalPagesBeforeCurrentChapter() {
      return calculateTotalPagesBeforeChapter(this, this.chapterId)
    },
    // 获取指定章节的目录数据以及章节内容数据
    getChapterCatalogDataByChapterId(chapterId) {
      // 使用递归方式查找，支持多级嵌套的目录结构
      function findChapter(chapters, id) {
        for (let i = 0; i < chapters.length; i++) {
          const chapter = chapters[i];
          if (chapter.chapterId === id) {
            return chapter;
          }
          if (chapter.children && chapter.children.length > 0) {
            const found = findChapter(chapter.children, id);
            if (found) {
              return found;
            }
          }
        }
        return null;
      }

      return findChapter(this.comprehensiveChapterAndCatalogData.chaptersData, chapterId);
    },
    setChaptersData(chaptersData, totalPages) {
      this.comprehensiveChapterAndCatalogData.chaptersData = chaptersData
      this.comprehensiveBookData.totalPages = totalPages
    },
    setCurrentPageIndex(currentPageIndex) {
      let convertedPageIndex = Number.parseInt(currentPageIndex)
      if (isNaN(convertedPageIndex)) {
        convertedPageIndex = undefined
      }
      this.comprehensiveBookData.currentPageIndex = convertedPageIndex
    },
    isLastPageInChapter() {
      const currentChapterInfo = this.getChapterCatalogDataByChapterId(this.chapterId)
      const pagesInTotalBeforeChatper = this.calculateTotalPagesBeforeCurrentChapter()
      return this.comprehensiveBookData.currentPageIndex - pagesInTotalBeforeChatper === currentChapterInfo.totalPages
    },
    isFirstPageInChapter() {
      const pagesInTotalBeforeChatper = this.calculateTotalPagesBeforeCurrentChapter()
      return this.comprehensiveBookData.currentPageIndex - pagesInTotalBeforeChatper === 1
    },
    getPreviousChapter() {
      return getChapterBasedOnIndex(this, 'previous')
    },
    getNextChapter() {
      return getChapterBasedOnIndex(this, 'next')
    },
    isFirstChapter() {
      const currentChapterIndexInCatalog = getChapterIndexInCatalog(this, this.chapterId)
      if (currentChapterIndexInCatalog <= 0) {
        return true
      }
      return false
    },
    jumpToPreviousChapter() {
      if (this.isFirstChapter()) {
        return Promise.reject(PAGE_TURNING_ERROR_MAPPER.FIRST_CHAPTER_IN_BOOK)
      }
      const currentChapterIndexInCatalog = this.comprehensiveChapterAndCatalogData.chaptersData.findIndex(
        chapterCatalogData => chapterCatalogData.chapterId === this.chapterId
      )
      const previousChapterCatalogData = this.comprehensiveChapterAndCatalogData.chaptersData[currentChapterIndexInCatalog - 1]
      return this.jumpToChapter(previousChapterCatalogData.chapterId)
    },
    isLastChapter() {
      const currentChapterIndexInCatalog = getChapterIndexInCatalog(this, this.chapterId)
      if (currentChapterIndexInCatalog >= this.comprehensiveChapterAndCatalogData.chaptersData.length - 1) {
        return true
      }
      return false
    },
    jumpToNextChapter() {
      debugger
      if (this.isLastChapter()) {
        return Promise.reject(PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK)
      }
      const currentChapterIndexInCatalog = this.comprehensiveChapterAndCatalogData.chaptersData.findIndex(
        chapterCatalogData => chapterCatalogData.chapterId === this.chapterId
      )
      const nextChapterCatalogData = this.comprehensiveChapterAndCatalogData.chaptersData[currentChapterIndexInCatalog + 1]
      return this.jumpToChapter(nextChapterCatalogData.chapterId)
    },
    // 跳转到指定章节指定页面, 默认第一页
    async jumpToChapter(chapterId, pageIndexInChapter = 1) {
      const chapterData = this.getChapterCatalogDataByChapterId(chapterId)
      if (!chapterData) {
        return Promise.reject(PAGE_TURNING_ERROR_MAPPER.CHAPTER_NOT_EXIST)
      }
      // 如果当前就是目标章节，以及目标页码，无需通过jumpToPage判断
      const cumulatedPagesInTotal = calculateTotalPagesBeforeChapter(this, chapterId)
      return this.jumpToPage(cumulatedPagesInTotal + pageIndexInChapter)
    },
    // 重用jumpToPage，根据nodeId跳转到所在的页面
    jumpToPageBasedOnNodeId(domId, targetChapterId) {
      return new Promise(async (resolve, reject) => {
        await this.loadChapterData(targetChapterId).catch(error => {
          reject(error)
          return error
        })
        findPageDataByNodeIdAndChapterId(
          this,
          targetChapterId,
          node => {
            return node.attrs?.id === domId
          },
          pageIndexInChapter => {
            const totalPages = calculateTotalPagesBeforeChapter(this, targetChapterId)
            // 找到node对应的章节中pageindex，则进行跳转
            this.jumpToPage(totalPages + pageIndexInChapter + 1)
              .then(resolve)
              .catch(reject)
          }
        )
      })
    },
    // 跳转到页面中指定page上
    async jumpToPage(pageIndexInBook, animateOpts = {}) {
      return new Promise(async (resolve, reject) => {
        // 检查检查该页码对应的章节内容是否加载
        const chapterData = getChapterDataByPageIndexInBook(this, pageIndexInBook)

        // 添加检查，确保chapterData存在
        if (!chapterData) {
          return reject(new Error('无法找到对应章节数据'))
        }

        if (pageIndexInBook === this.comprehensiveBookData.currentPageIndex) {
          return resolve(null)
        }

        // if (!chapterData.chapterContent) {
        await this.loadChapterData(chapterData.chapterId).catch(reject)
        // }
        resolve(null)
        nextTick(() => {
          jumpToPageIndexInBook(this, pageIndexInBook, animateOpts)
        })
      })
    },
    // 获取整本教材中页码在章节中的index
    getCumulatedPageCntInPreviousChapters(chapterId) {
      return calculateTotalPagesBeforeChapter(this, chapterId)
    },
    getPageIndexInChapterByPageIndexInBook(pageIndex) {
      return pageIndex - this.calculateTotalPagesBeforeCurrentChapter()
    },
    getFirstChapterCatalogData() {
      return this.comprehensiveChapterAndCatalogData.chaptersData[0]
    },
    // 视频组件计数+
    incrementVideoCount() {
      this.videoComponentCount++
    },
    // 视频组件计数-
    decrementVideoCount() {
      this.videoComponentCount--
    }
  }
})

export default useReader
