/**
 * PostMessage 通信处理工具（子页面用）
 * 用于阅读器页面与父页面的跨域通信
 */

// const ALLOWED_PARENT_ORIGIN = null; // 测试的时候使用null
const ALLOWED_PARENT_ORIGIN = new URL(import.meta.env.VITE_PARENT_ORIGIN).origin;
console.log("VITE_PARENT_ORIGIN =", import.meta.env.VITE_PARENT_ORIGIN);


let parentOrigin = null;
let isConnectedToParent = false;
/**
 * 向父页面发送消息
 * @param {Object} data - 要发送的消息数据
 */
export function sendMessageToParent(data) {
  try {
    if (!parentOrigin) {
      console.log("父页面 origin 未知，消息可能无法送达:", data);
    }
    const targetOrigin = parentOrigin || ALLOWED_PARENT_ORIGIN || "*";
    if (!/^https?:\/\/.+/.test(targetOrigin) && targetOrigin !== "*") {
      console.error("无效的 targetOrigin:", targetOrigin);
      return;
    }
    window.parent.postMessage(data, targetOrigin);
    console.log("向父页面发送消息:", data, "目标:", targetOrigin);
  } catch (error) {
    console.error("发送消息到父页面失败:", error);
  }
}

/**
 * 处理滚动到元素的请求
 * @param {Object} data - 消息数据
 * @param {Function} highlightKeyWordSmooth - 高亮函数
 */
export function handleScrollToElement(data, highlightKeyWordSmooth) {
  const domId = data.payload?.domId;
  if (!domId) return;
  
  let targetHeadingDom = document.querySelector(
      `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${domId}"]`
  );

  // 节头跳转问题修改
  if (!targetHeadingDom) {
    targetHeadingDom = document.querySelector(
        `div[id="${domId}"]`
    );
  }
  if (targetHeadingDom) {
    highlightKeyWordSmooth(targetHeadingDom);
    // 发送滚动完成消息
    sendMessageToParent({
      type: 'user_interaction',
      action: 'scroll_to_element',
      domId: domId,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 处理打印请求
 * @param {string} containerId - 容器ID
 * @param {Function} printJS - 打印函数
 */
export async function handlePrintRequest(containerId) {
  const container = document.getElementById(containerId);
  if (!container) {
    console.error("未找到打印容器:", containerId);
    return;
  }

  // 动态引入 print-js（只在需要打印时加载）
  const { default: printJS } = await import("print-js");

  const canvases = container.querySelectorAll("canvas");
  const canvasArray = Array.from(canvases);

  // 移除 canvas
  canvasArray.forEach(canvas => canvas.remove());

  printJS({
    printable: containerId,
    type: "html",
    scanStyles: false,
    ignoreElements: ["canvas"],
    targetStyles: ["*"],
    onPrintDialogClose: () => {
      // 打印完成后恢复 canvas
      canvasArray.forEach(canvas => container.appendChild(canvas));
    }
  });

  // 发送打印完成消息
  sendMessageToParent({
    type: "user_interaction",
    action: "print_completed",
    timestamp: new Date().toISOString()
  });
}

/**
 * 处理初始化数据
 * @param {Object} data - 初始化数据
 */
export function handleInitData(data) {
  console.log('收到初始化数据:', data);
  
  if (data.bookId) {
    console.log('接收到书籍ID:', data.bookId);
  }
  if (data.chapterId) {
    console.log('接收到章节ID:', data.chapterId);
  }
  if (data.auditType) {
    console.log('接收到审核类型:', data.auditType);
  }
  
  // 发送数据接收确认
  sendMessageToParent({
    type: 'user_interaction',
    action: 'init_data_received',
    receivedData: {
      bookId: data.bookId,
      chapterId: data.chapterId,
      auditType: data.auditType
    },
    timestamp: new Date().toISOString()
  });
}

/**
 * 统一的消息处理函数
 * @param {MessageEvent} event - 消息事件
 * @param {Object} handlers - 处理函数集合
 */
export function handleParentMessage(event, handlers = {}) {
  // 安全校验：确认消息来源
  if (ALLOWED_PARENT_ORIGIN && event.origin !== ALLOWED_PARENT_ORIGIN) {
    console.log("拒绝未知来源消息:", event.origin, event.data);
    return;
  }
  // 首次连接时，保存 parentOrigin
  if (!parentOrigin) {
    parentOrigin = event.origin;
    isConnectedToParent = true;
  }


  // 处理不同类型的消息
  if (event.data && event.data.type) {
    switch (event.data.type) {
      case "SCROLL_TO_ELEMENT":
        handlers.handleScrollToElement?.(event.data);
        break;

      case "PRINT":
        handlers.handlePrintRequest?.();
        break;

      case "parent_ready":
        console.log("父页面已准备就绪");
        sendMessageToParent({
          type: "iframe_ready",
          message: "iframe已准备就绪",
          timestamp: new Date().toISOString()
        });
        break;

      case "init_data":
        handlers.handleInitData?.(event.data);
        break;

      case "update_content":
        console.log("更新内容请求:", event.data);
        handlers.handleUpdateContent?.(event.data);
        break;

      case "test_message":
        console.log("收到测试消息:", event.data.message);
        sendMessageToParent({
          type: "user_interaction",
          action: "test_message_received",
          message: "测试消息已收到",
          timestamp: new Date().toISOString()
        });
        break;

      default:
        console.log("未知消息类型:", event.data.type);
    }
  }
}

/**
 * 设置 postMessage 监听器
 * @param {Object} handlers - 处理函数集合
 */
export function setupPostMessageListener(handlers = {}) {
  window.addEventListener("message", (event) => {
    handleParentMessage(event, handlers);
  });
  console.log("PostMessage 监听器已设置");
}

/**
 * 发送页面加载完成消息
 */
export function sendPageLoadedMessage() {
  sendMessageToParent({
    type: "iframe_ready",
    message: "iframe页面加载完成",
    timestamp: new Date().toISOString()
  });
}

/**
 * 发送内容加载完成消息
 */
export function sendContentLoadedMessage() {
  sendMessageToParent({
    type: 'content_loaded',
    message: '内容加载完成',
    timestamp: new Date().toISOString()
  });
}

/**
 * 发送错误消息
 * @param {string|Error} error - 错误信息
 */
export function sendErrorMessage(error) {
  sendMessageToParent({
    type: 'iframe_error',
    error: error.message || error,
    timestamp: new Date().toISOString()
  });
}

/**
 * 发送用户交互消息
 * @param {string} action - 交互动作
 * @param {Object} data - 额外数据
 */
export function sendUserInteractionMessage(action, data = {}) {
  sendMessageToParent({
    type: 'user_interaction',
    action: action,
    ...data,
    timestamp: new Date().toISOString()
  });
}

/**
 * 获取连接状态
 */
export function getConnectionStatus() {
  return {
    isConnected: isConnectedToParent,
    parentOrigin: parentOrigin
  };
}

/**
 * 设置全局错误处理
 */
export function setupGlobalErrorHandling() {
  // 捕获JavaScript错误
  window.addEventListener('error', function(event) {
    if (event.message?.includes("Blocked a frame with origin")) {
      console.warn("跨域安全警告:", event.message);
      return; // 不上报
    }
    sendErrorMessage({
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  });

  // 捕获Promise rejection
  window.addEventListener('unhandledrejection', function(event) {
    sendErrorMessage({
      message: 'Promise rejection: ' + event.reason,
      type: 'unhandledrejection'
    });
  });
}

// 默认导出所有函数
export default {
  sendMessageToParent,
  handleScrollToElement,
  handlePrintRequest,
  handleInitData,
  handleParentMessage,
  setupPostMessageListener,
  sendPageLoadedMessage,
  sendContentLoadedMessage,
  sendErrorMessage,
  sendUserInteractionMessage,
  getConnectionStatus,
  setupGlobalErrorHandling
};
