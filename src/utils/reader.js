import { ElMessageBox, ElMessage } from 'element-plus'
import { BOOK_CHAPTER_EXPENSE, BOOK_USER_PURCHASE } from '@/constants/book'

// import useSiteStore from '@/store/modules/site'
import { getContent, getContentSimple } from '@/api/book/reader'
// import { encryptWithKey } from '@/utils/jsencrypt';
import { getSectionIdName } from "@/utils/index.js";
import mitt from 'mitt'

export function gotoReader(bookId) {
  /*const publicKey = useSiteStore().publicKey;
    if (!publicKey) {
      console.error('无法获取公钥');
      return;
    }
    // 使用公钥加密数据
    const encryptedBookId = encryptWithKey(bookId, publicKey);
    if (!encryptedBookId) {
      console.error('数据加密失败');
      return;
    }数据加密失败*/
  /*router.push({
    path: '/reader', query: { k:  bookId}
  })*/
  window.open(`/reader?k=${bookId}`, '_blank')
}

/**
 * @description 按目录的顺序，对书签列表重新排列。
 * @param {*} bookmarkDataList
 * @param {*} catalogDataList
 * @returns
 */
export function sortBookmarkList(bookmarkDataList, catalogDataList) {
  const newBookmarkDataList = JSON.parse(JSON.stringify(bookmarkDataList))
  newBookmarkDataList.sort((a, b) => {
    if (a.chapterId === b.chapterId) {
      return a.pageNumber - b.pageNumber
    }
    const aCatalogIndex = catalogDataList.findIndex(chapterItem => chapterItem.chapterId === a.chapterId)
    const bCatalogIndex = catalogDataList.findIndex(chapterItem => chapterItem.chapterId === b.chapterId)
    return aCatalogIndex - bCatalogIndex
  })
  return newBookmarkDataList
}

export function sortSearchedChapterGroupList(searchedChapterGroupList, catalogDataList) {
  searchedChapterGroupList = JSON.parse(JSON.stringify(searchedChapterGroupList))
  searchedChapterGroupList.sort((a, b) => {
    const aCatalogIndex = catalogDataList.findIndex(chapterItem => chapterItem.chapterId === a.chapterId)
    const bCatalogIndex = catalogDataList.findIndex(chapterItem => chapterItem.chapterId === b.chapterId)
    return aCatalogIndex - bCatalogIndex
  })
  searchedChapterGroupList.forEach(searchedChapterGroupItem => {
    searchedChapterGroupItem.data.sort((a, b) => {
      return a.pageNumber - b.pageNumber
    })
  })
  return searchedChapterGroupList
}

export function findClosestPageNode(contentNode) {
  return contentNode.closest('.Pages-item-main')
}

export const PAGE_CONTAINER_CSS_CLASS = 'dutp-page-container'

export const HEADING_ATTR_ID = 'data-toc-id'
export const PAGE_CHAPTER_ID_ATTR = 'data-chapter-id'

export const PAGE_ITEMS_CONTAINER_ID = 'book-reading-container'
export const PAGE_ITEMS_CSS_CLASS = 'book-reading-page-item__'

export const CHAPTER_PAGE_NUMBER = 'data-page-number-chapter'
export const BOOK_PAGE_INDEX = 'data-page-index-in-book'

export const TEXT_SELECTION_TOOLBAR_ID = 'text-selection-toolbar'

export const PAGE_TURNING_ERROR_MAPPER = {
  FIRST_PAGE_IN_BOOK: {
    code: '001',
    msg: '已经是教材第一页'
  },
  LAST_PAGE_IN_BOOK: {
    code: '002',
    msg: '已经是教材最后一页'
  },
  CHAPTER_NOT_EXIST: {
    code: '003',
    msg: '该章节不存在'
  },
  PAGEINDEX_NOT_EXIST: {
    code: '004',
    msg: '该页码不存在'
  },
  CHAPTER_DATA_NOT_EXIST: {
    code: '006',
    msg: '该章节对应目录数据不存在'
  },
  FIRST_CHAPTER_IN_BOOK: {
    code: '007',
    msg: '已经是教材第一章节'
  },
  LAST_CHAPTER_IN_BOOK: {
    code: '008',
    msg: '已经是教材最后章节'
  },
  NEED_TO_PAY_TO_READ: {
    code: 422,
    msg: '该章节是付费章节'
  }
}

export const BOOK_MASTER_FLAG = {
  OTHER: '1',
  PRIMARY_BOOK: '2',
  SECONDARY_BOOK: '3'
}

// 待废弃，需求上没有呼吸灯高亮显示跳转到的笔记内容功能
export function highlightKeyWordSmooth(domOrDomId, opts = { duration: 2000 }) {
  if (typeof domOrDomId === 'string') {
    domOrDomId = [document.querySelector(domOrDomId)]
  } else if (domOrDomId instanceof Node) {
    domOrDomId = [domOrDomId]
  } else if (Object.prototype.toString.call(domOrDomId) === '[object Array]') {
    const renewedDomArr = []
    domOrDomId.forEach(domItem => {
      if (typeof domItem === 'string') {
        renewedDomArr.push(document.querySelector(domItem))
      } else if (typeof domItem === 'object') {
        renewedDomArr.push(domItem)
      }
    })
    domOrDomId = renewedDomArr
  }
  //定时器为后加的，解决布局未稳定造成的偶发性跳转异常的问题
  setTimeout(() => {
    domOrDomId[0]?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }, 100)
}

/**
 * @description 根据笔记或者划线的fromWordId跳转到教材内容区域
 * @param {*} fromWordId
 */
export function jumpToTextByFromWordId(fromWordId) {
  const textId = fromWordId.split(TEXT_ID_DELIMITER);
  const targetDom = document.querySelector('#' + textId[0])
  targetDom?.scrollIntoView({ behavior: 'smooth', block: 'center' })
}

export function getChapterContent(bookId, chapterId, chapterName) {
  return getContent({ id: chapterId }).then(resp => {
    if (PAGE_TURNING_ERROR_MAPPER.NEED_TO_PAY_TO_READ.code === resp.code) {
      needToPayToRead(bookId, chapterName)
      return Promise.reject(resp)
    }
    // ElMessage.error(resp.msg)
    return resp
  })
}

export function getChapterContentInSimple(bookId, chapterId, chapterName, contentVersion) {
  const params = { id: chapterId };

  // 如果传递了 contentVersion 参数，添加到请求参数中
  if (contentVersion !== undefined && contentVersion !== null) {
    params.contentVersion = contentVersion;
  }

  return getContentSimple(params).then(resp => {
    if (resp.code === 200) {
      return resp
    }
    return Promise.reject(resp)
  })
}

export function needToPayToRead(bookId, chapterName) {
  return ElMessageBox.confirm(`章节"${chapterName}"是付费章节，是否购买？`, '付费章节', {
    confirmButtonText: '去购买',
    cancelButtonText: '放弃',
    type: 'warning'
  })
}

export function generatePaidPageContent() {
  return {
    content: [
      {
        type: 'page',
        attrs: {
          id: '8z74h0o1'
        },
        content: [
          {
            type: 'paragraph',
            attrs: {
              id: 'i6pa1rjj'
            },
            content: [
              {
                type: 'text',
                text: '该章节是收费章节，请购买阅读'
              }
            ]
          }
        ]
      }
    ]
  }
}

export function hasAccessToReadChapter(ele) {
  if (ele.free === BOOK_CHAPTER_EXPENSE.FREE_READ) {
    return true
  }
  if (ele.free === BOOK_CHAPTER_EXPENSE.PAY_TO_READ && ele.hasBuy !== BOOK_USER_PURCHASE.NOT_PAID) {
    return true
  }
  return false
}

export function whenLoaded(fn) {
  if (document.readyState === 'complete') {
    setTimeout(fn, 0) // 异步执行以保持一致性
  } else {
    window.removeEventListener('load', fn)
    window.addEventListener('load', fn)
  }
}

let keyboardEventQueue = []
let registeredOnDocument = false
let isProcessing = false

function keydownEventHandler(e) {
  if (!isProcessing) {
    processQueue(e)
  }
}

async function processQueue(e) {
  isProcessing = true
  const length = keyboardEventQueue.length
  let indexer = 0
  while (indexer < length) {
    const task = keyboardEventQueue[indexer]
    try {
      if (e.cancelBubble) {
        break
      }
      await task.fn(e)
      indexer++
    } catch (error) {
      console.error(`Error processing task ${task.name}:`, error)
      break
    }
  }
  isProcessing = false
  return true
}

export function unregister() {
  document.removeEventListener('keydown', keydownEventHandler)
  keyboardEventQueue = []
  registeredOnDocument = false
}

export function unregisterKeyboardEvent(featureName) {
  const findedIndex = keyboardEventQueue.findIndex(item => item.name === featureName)
  if (findedIndex > -1) {
    keyboardEventQueue.splice(findedIndex, 1)
  }
}

export function registerKeyboardEvent(featureName, fn, opts) {
  if (!registeredOnDocument) {
    document.addEventListener('keydown', keydownEventHandler)
    registeredOnDocument = true
  }
  if (keyboardEventQueue.find(item => item.name === featureName)) {
    // 已添加的事件不重复添加
    return false
  }
  let nEventPosition = keyboardEventQueue.length
  if (opts?.before) {
    const findedIndex = keyboardEventQueue.findIndex(item => item.name === opts.before)
    if (findedIndex > -1) {
      nEventPosition = findedIndex
    }
  }
  const newTask = {
    name: featureName,
    fn: e => {
      return new Promise((resolve, reject) => {
        try {
          fn(e)
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    }
  }

  // 插入任务到指定位置
  keyboardEventQueue.splice(nEventPosition, 0, newTask)

  return true
}

export const TEXT_ID_DELIMITER = ';_;'

/**
 * @description 从选中的节点id列表中获取起始和结束的节点id列表, 主要用于保存笔记或划线，以及从笔记数据和划线数据中完全匹配数据
 */
export function deriveFromAndEndWordIdList(selectedNodeIds) {
  let textParagraphId;
  let previousTextId;
  let fromWordIdList = [];
  let endWordIdList = [];
  selectedNodeIds = selectedNodeIds.filter(textId => !!textId)
  const len = selectedNodeIds.length;
  selectedNodeIds.forEach((textId, index) => {
    const slicedTextIds = textId.split('_')
    if (!textParagraphId) {
      fromWordIdList.push(textId)
      previousTextId = textParagraphId = slicedTextIds[0];
      return;
    }
    if (slicedTextIds[0] && textParagraphId !== slicedTextIds[0]) {
      textParagraphId = slicedTextIds[0]
      fromWordIdList.push(textId)
      endWordIdList.push(previousTextId)
    }
    if (index === len - 1) {
      endWordIdList.push(textId)
    }
    previousTextId = textId;
  });
  return {
    fromWordIdList: fromWordIdList.join(TEXT_ID_DELIMITER),
    endWordIdList: endWordIdList.join(TEXT_ID_DELIMITER)
  }
}

/**
 * @description 填充笔记id, 使fromwordid到endwordid完整化，从而可以比对选取内文字与现有笔记id或者划线id是否重合
 */
export function fillinWordIdsFromWordIdWithDelimiter(fromWordIdWithDelimiter, endWordIdWithDelimiter) {
  if (!fromWordIdWithDelimiter || !endWordIdWithDelimiter) {
    return [];
  }
  const chunkedFromWordIds = fromWordIdWithDelimiter.split(TEXT_ID_DELIMITER);
  const chunkedEndWordIds = endWordIdWithDelimiter.split(TEXT_ID_DELIMITER);
  let noteIdNameList = [];
  for (let i = 0, len = Math.min(chunkedFromWordIds.length, chunkedEndWordIds.length); i < len; i++) {
    noteIdNameList = noteIdNameList.concat(getSectionIdName(chunkedFromWordIds[i], chunkedEndWordIds[i]));
  }
  return noteIdNameList;
}

/**
 * @description 检查选取内文字与笔记id或者划线id是否重合
 */
function existesMarkAndNoteData(elementIdList, markNoteDataList, idAttr) {
  const toBeDeletedNote = new Set();
  markNoteDataList.forEach((noteItem) => {
    const wordIds = fillinWordIdsFromWordIdWithDelimiter(noteItem.fromWordId, noteItem.endWordId)
    wordIds.forEach((wordId) => {
      if (elementIdList.indexOf(wordId.substring(1)) >= 0) {
        toBeDeletedNote.add(noteItem[idAttr])
      }
    })
  });
  return Array.from(toBeDeletedNote.values());
}

export function deriveIntersectionLinemarksData(elementIdList, markNoteDataList) {
  return existesMarkAndNoteData(elementIdList, markNoteDataList, 'lineId');
}

export function deriveIntersectionNotesData(elementIdList, markNoteDataList) {
  return existesMarkAndNoteData(elementIdList, markNoteDataList, 'noteId');
}

const channelEventMap = mitt()

export const TEST_PAPER_SUBMIT_EVENT = 'test-paper-submit-event';

export function registerSubmitEvent(eventName, fn) {
  channelEventMap.on(eventName, fn);
}

export function unregisterSubmitEvent(eventName, fn) {
  channelEventMap.off(eventName, fn)
}

export function broadCastChannel(eventName, data) {
  channelEventMap.emit(eventName, data);
}
