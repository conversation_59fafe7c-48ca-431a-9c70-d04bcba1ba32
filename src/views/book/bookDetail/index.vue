<template>
  <div class="book-detail-con">
    <div class="search-con">
      <searchBar @search="doToSearchPage" :isNoShadow="true" />
    </div>
    <div class="data-detail-con">
      <div class="type-level-con">
        <typeLevelCrumbs :item="bookItem" />
      </div>
      <div class="data-info-con">
        <div class="data-info-left">
          <div class="book-info-con">
            <img
              class="book-img"
              :src="currentBook.cover ? currentBook.cover : bookCoverDefault"
              alt=""
              @click="coverClick"
            />
            <div class="line-top" />
            <div class="line-middle" />
            <div class="line-bottom" />
          </div>
          <div class="btn-con">
            <div class="collection-btn" @click="doCollect">
              <!--  <el-icon class="icon-sty"><Star/></el-icon>-->
              <img
                class="icon-img"
                src="@/assets/images/book/start-icon-line.png"
                alt=""
                v-if="!isCollectedFlag"
              />
              <img
                class="icon-img"
                src="@/assets/images/book/start-icon-yellow.png"
                alt=""
                v-else
              />
              <span class="icon-text">{{
                !isCollectedFlag ? "收藏" : "已收藏"
              }}</span>
            </div>
            <div
              class="source-btn"
              @click="toNaturalResourcesPage(currentBook)"
              v-if="currentBook.shelfState !== 4"
            >
              <img
                class="icon-img"
                src="@/assets/images/book/source-icon.png"
                alt=""
              />
              <span class="icon-text">资源包</span>
            </div>
          </div>
          <div class="purchase-notes">
            <div class="purchase-notes-title">购买须知</div>
            <div class="purchase-notes-content">
              数字化商品不支持七天无理由退换<br />
              版权保护，不提供PDF或EPUB等源文件，不可打印
            </div>
          </div>
        </div>
        <div class="data-info-right">
          <div class="right-top-con">
            <div class="info-text-con">
              <div class="right-title">{{ currentBook.bookName }}</div>
              <div class="authors-info">
                {{
                  currentBook.authorValue
                    ? currentBook.authorLabel + ":" + currentBook.authorValue
                    : ""
                }}
              </div>
              <div class="item-info-con">
                <div class="item-row-con">
                  <div class="item-col">
                    <div class="label">ISBN/ISSN:</div>
                    <div class="text">
                      {{
                        currentBook.isbn ? currentBook.isbn : currentBook.issn
                      }}
                    </div>
                  </div>
                  <div class="item-col">
                    <div class="label">出版单位:</div>
                    <div class="text">{{ currentBook.houseName }}</div>
                  </div>
                </div>
                <div class="item-row-con">
                  <div class="item-col">
                    <div class="label">出版日期:</div>
                    <div class="text">{{ currentBook.publishDate }}</div>
                  </div>
                  <div class="item-col">
                    <div class="label">出版状态:</div>
                    <div class="text">
                      {{
                        currentBook.publishStatus === 1 ? "未出版" : "已出版"
                      }}
                    </div>
                  </div>
                </div>
                <div class="item-row-con">
                  <div class="item-col">
                    <div class="label">合作院校:</div>
                    <div class="text">{{ currentBook.schoolName }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="info-orc-con">
              <!--  手机二维码阅读 -->
              <!-- <div class="orc-item">
                <div class="orc-top">
                  <img class="orc-img" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/test/or-code-01.png" alt="" />
                </div>
                <div class="orc-bottom">
                  <el-icon class="phone-icon">
                    <Iphone />
                  </el-icon>
                  <span class="phone-text">手机阅读</span>
                </div>
              </div>-->
            </div>
          </div>
          <div class="right-middle-con">
            <div class="price-info-con" v-if="currentBook.shelfState === 1">
              <div class="sale-price">
                价格：<span>{{
                  currentBook.priceSale > 0
                    ? "￥" + currentBook.priceSale
                    : "免费试读"
                }}</span>
              </div>
              <div class="fix-price">
                定价：<span>{{
                  currentBook.priceCounter
                    ? "￥" + currentBook.priceCounter
                    : ""
                }}</span>
              </div>
            </div>
            <div class="price-info-con" v-else>
              <div class="fix-price-shelfState">
                定价：<span>{{
                  currentBook.priceCounter
                    ? "￥" + currentBook.priceCounter
                    : ""
                }}</span>
              </div>
            </div>
            <div class="read-num-con" v-if="currentBook.shelfState === 1">
              <el-icon class="read-num-icon">
                <Iphone />
              </el-icon>
              <span>阅读量：{{ currentBook.readQuantity }}</span>
            </div>
          </div>
          <div class="right-bottom-con" v-if="currentBook.deputyBookList">
            <div class="supporting-textbooks-con">
              <div class="title">配套教材</div>
              <supportingTextbooksBanner
                :deputyBookList="currentBook?.deputyBookList"
                :key="reloadKey"
                :isShow="isShow"
                @update="supportingTextbooks"
              />
            </div>
          </div>
          <div
            :style="{ marginTop: currentBook.deputyBookList ? '0px' : '83px' }"
          >
            <div
              class="right-bottom-btn-con"
              v-if="currentBook.shelfState === 4 && currentBook.addWay === null"
            >
              <div class="btn-white">即将上架</div>
            </div>
            <div
              class="right-bottom-btn-con"
              v-else-if="
                (currentBook.shelfState === 1 && currentBook.addWay === null) ||
                (currentBook.shelfState === 1 &&
                  currentBook.addWay !== null &&
                  currentBook.expireDate)
              "
            >
              <!-- 功能暂时下线，待参数配置上线再上线 -->
              <!-- <div class="btn-blue" v-if="currentBook.priceSale > 0" @click="showBuyPopup">购买</div> -->
              <div class="btn-blue" @click="exchange">购书码</div>
              <div class="btn-white" @click="applicationForUse">试用申请</div>
            </div>
            <div
              class="right-bottom-btn-con"
              v-else-if="currentBook.addWay !== null && !currentBook.expireDate"
            >
              <div class="btn-blue" @click="goStudy">去学习</div>
            </div>
          </div>
        </div>
      </div>
      <div class="introduction-catalog-con">
        <div
          class="introduction-con"
          :style="{ width: currentBook.shelfState === 1 ? '60%' : '100%' }"
          :class="[
            { show: showIntroductionFlag },
            { hidden: !showIntroductionFlag },
          ]"
          v-show="showIntroductionFlag"
          v-if="masterFlag"
        >
          <div class="head-title">
            <div class="title">图书介绍</div>
          </div>
          <div class="introduction-text" v-if="editorsChoice">
            <div class="label">
              编辑推荐<img
                class="icon-img"
                style="margin: 0px 0px 5px 10px"
                src="@/assets/images/thumb.png"
                alt=""
              />
            </div>
            <div class="i-content">{{ editorsChoice.recommend }}</div>
            <div class="label">内容简介</div>
            <div class="i-content">{{ editorsChoice.introduce }}</div>
            <div class="label">版权信息</div>
            <div class="i-content">{{ editorsChoice.copyright }}</div>
            <div class="label">版权声明</div>
            <div class="i-content">{{ editorsChoice.declaration }}</div>
          </div>
        </div>
        <div class="catalog-con" v-if="currentBook.shelfState === 1">
          <!--          <div class="left-btn" @mousedown="onMouseDown">-->
          <div
            class="left-btn"
            @click="showIntroductionFlag = !showIntroductionFlag"
          >
            <div class="right-arrow" v-if="showIntroductionFlag" />
            <div class="left-arrow" v-else />
          </div>
          <div class="head-title">
            <div class="title-left">
              <div class="title">目录</div>
            </div>
            <div class="title-right">
              <div class="title-btn" @click="changExpandAllFlag">
                {{ expandAllFlagData ? "收起" : "展开" }}全部目录
              </div>
            </div>
          </div>
          <div
            class="catalog-tree-list-con"
            :style="{ 'min-width': catalogWidth + 'px' }"
          >
            <catalog-tree-list
              :dataObj="chapterData"
              :expandAllFlag="expandAllFlagData"
            />
          </div>
        </div>
      </div>
      <!-- 相关教材 -->
      <div class="related-textbooks-con">
        <div class="related-textbooks">
          <div class="title-con">
            <div class="title">相关教材</div>
            <div class="more-btn" @click="moreRecommendations">更多推荐</div>
          </div>
          <div class="textbooks-list-con">
            <relatedTextbooksList
              :searchStr="searchTerm"
              @update="handleUpdate"
            />
          </div>
        </div>
      </div>
      <!--  弹窗    -->
      <Payment v-model="showBuyDialogFlag" :form="currentBook" />
    </div>
  </div>
  <el-dialog
    v-model="promptDialogVisible"
    style="margin-top: 30vh !important"
    width="500"
    @close="promptDialogVisible = false"
  >
    <template #header>
      <span>
        <img src="@/assets/icons/svg/prompt.svg" style="margin: 0 5px -3px 0" />
        提示
      </span>
    </template>
    <span
      >教材试用申请为期{{
        siteConfigInfo?.trialDayLimit
      }}天，该功能仅限认证教师使用。 <br />如果您是教师，
      <el-link href="/identity" style="color: #409eff" target="_blank"
        >请点击这里完成教师认证</el-link
      ></span
    >
  </el-dialog>
  <el-dialog
    v-model="trialDialogVisible"
    title="试用申请"
    width="500px"
    @close="trialDialogClose"
  >
    <div>
      <el-alert
        :title="
          '我们提供为期' +
          siteConfigInfo?.trialDayLimit +
          '天的教材试用机会。请您上传以下申请材料'
        "
        type="warning"
        show-icon
        class="mb-4"
        :closable="false"
      />
      <el-form
        :model="formData"
        ref="formRef"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="申请原因" prop="applicationReason">
          <el-input
            type="textarea"
            v-model="formData.applicationReason"
            placeholder="请输入申请原因"
            rows="3"
          />
        </el-form-item>
        <el-form-item label="二级学院盖章" label-width="120px" prop="examineFileList">
          <el-upload
            ref="uploadRef"
            v-model:file-list="formData.examineFileList"
            :http-request="upload"
            :action="uploadUrl"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :on-exceed="handleBeforeUpload"

            :limit="1"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传pdf、png、jpeg格式文件,不超过20M
              </div>
            </template>
            <!-- 自定义文件列表项 -->
            <template #file="{ file }">
              <div class="custom-file-item">
                <el-image
                  :src="iconUrl ? iconUrl : loding"
                  style="width: 30px; height: 30px"
                />
                <span @click="previewFile(file)" style="margin-right: 120px">{{ file.name }}</span>
                <el-icon @click="handleRemove(file)" ><CloseBold /></el-icon>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" style="text-align: center">
      <el-button @click="cancelDialog">取消</el-button>
      <el-button type="primary" :disabled="!sbmFlg" @click="submitForm">提交</el-button>
    </div>
  </el-dialog>
</template>

<script setup name="BookDetail">
import { ElNotification, ElMessageBox } from "element-plus";
import typeLevelCrumbs from "@/views/book/components/typeLevelCrumbs/index.vue";
import supportingTextbooksBanner from "@/views/book/components/supportingTextbooksBanner/index.vue";
import searchBar from "@/views/home/<USER>/SearchBar/index.vue";
import catalogTreeList from "@/views/book/components/catalogTreeList/index.vue";
import relatedTextbooksList from "@/views/book/components/relatedTextbooksList/index.vue";
import Payment from "@/components/payment/index.vue";
import {
  getDtbBookInfo,
  getRecommendedTextbooks,
  queryBookChapterListByBookDetail,
} from "@/api/openApi/openApi";
import { submitTrialApplicationEducation } from "@/api/shop/trial";
import { addPrint } from "@/api/book/print";
import { onMounted, ref } from "vue";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user.js";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
import loding from "@/assets/images/loding.png";
import { OssService } from "@/utils/aliOss.js";
import { getSiteConfig } from "@/api/system/siteConfig";
import { addFile } from "@/api/system/file";
import {
  listCollect,
  addDtbBookCollect,
  delDtbBookCollect,
} from "@/api/book/collect";
import pdf from "@/assets/svg/pdf.svg";
import img from "@/assets/svg/img.svg";
import { Base64 } from "js-base64";
import useSiteStore from "@/store/modules/site";
const subjectList = computed(() => useSiteStore().subjectList);
const reloadKey = ref(0);
const bookItem = ref(null);
const userInfo = ref();
const siteConfigStore = ref(null);
const siteConfigInfo = ref(null);
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
import { gotoReader } from "@/utils/reader";
const router = useRouter();
// 获取当前路由对象
const route = useRoute();
const bookIdDecrypt = ref();
// 从路由对象的 query 中获取 bookId 参数
const bookId = route.query.key;
const assistantBookId = route.query.assistantKey;
const assistantBookIdDecrypt = ref();
const currentBook = ref({});
const editorsChoice = ref({});
const chapterData = ref([]);
const promptDialogVisible = ref(false);
const trialDialogVisible = ref(false);
const formData = reactive({
  applicationReason: "",
  examineFileList: [],
});
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const hoverBookIndex = ref(); // 鼠标悬停的教材索引
const searchTerm = ref("");
const masterFlag = ref(true);
const showBuyDialogFlag = ref(false);
let currentBookItemIndex = ref(0);
let showIntroductionFlag = ref(true);
let isCollectedFlag = ref(false);
let expandAllFlagData = ref(false);
let catalogWidth = ref(520);
let mouseDown = ref(false);
let initialMouseX = ref(0);
let initialWidth = ref(0);
const visibleBooks = ref([]);
let currentBookcount = ref(0);
const invoiceForm = ref({});
const iconUrl = ref();
const sbmFlg = ref(false);
const rules = reactive({
  applicationReason: [
    { required: true, message: "请输入申请原因", trigger: "blur" },
  ],
  examineFileList: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          callback(new Error("请上传二级学院盖章文件"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
});
// 切换到上一本教材
// function prevBookItem() {
//   if (currentBookItemIndex.value > 0) {
//     currentBookItemIndex.value--;
//   } else {
//     currentBookItemIndex.value = currentBook.value.deputyBookList.length - 1;
//   }
// }
// 切换到下一本教材
// function nextBookItem() {
//   if (currentBookItemIndex.value < currentBook.value.deputyBookList.length - 1) {
//     currentBookItemIndex.value++;
//   } else {
//     currentBookItemIndex.value = 0;
//   }
// }
// 预览文件
const previewFile = (item) => {
  // 实现预览文件逻辑
  const url = item.response.url;
  const encodedUrl = Base64.encode(url);
  const previewUrl =
    import.meta.env.VITE_ONLINE_PREVIEW + encodeURIComponent(encodedUrl);
  window.open(previewUrl);
};
function nextBookItem() {
  currentBookItemIndex.value++;
  if (
    currentBookItemIndex.value % 10 === 0 &&
    currentBookItemIndex.value < currentBookcount.value
  ) {
    updateVisibleBooks();
  }
  if (currentBookcount.value <= currentBookItemIndex.value) {
    currentBookItemIndex.value = currentBookcount.value - 1;
  }
}

function hoverBookItem(index) {
  hoverBookIndex.value = index;
}

// 更新当前配套教材索引
function updateCurrentBookItemIndex(index) {
  currentBookItemIndex.value = index;
}

function prevBookItem() {
  if (currentBookItemIndex.value > 0) {
    let parseIntValue = parseInt(currentBookItemIndex.value.toString()[0], 10);
    currentBookItemIndex.value--;
    if ((currentBookItemIndex.value + 1) / 10 === parseIntValue) {
      visibleBooks.value = currentBook.value.deputyBookList.slice(
        currentBookItemIndex.value - 9,
        currentBookItemIndex.value + 1
      );
    }
  }
}

function updateVisibleBooks() {
  visibleBooks.value = currentBook.value.deputyBookList.slice(
    currentBookItemIndex.value,
    currentBookItemIndex.value + 10
  );
}

function upload(file) {
  return syncFile(file);
}

const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};
const handleRemove = (uploadFile, uploadFiles) => {
  // 从文件列表中移除被删除的文件
  const index = formData.examineFileList.findIndex(file => file.uid === uploadFile.uid);
  if (index !== -1) {
    formData.examineFileList.splice(index, 1);
  }
  
  // 重置相关状态
  sbmFlg.value = false;
  iconUrl.value = null; // 重置文件类型图标
};

const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

// 上传成功处理
function handleUploadSuccess(res, file) {
  sbmFlg.value = true;
  if (file.name.split(".").pop() === "pdf") {
    iconUrl.value = pdf;
  } else {
    iconUrl.value = img;
  }
}

// 上传失败处理
function handleUploadError() {}
const uploadRef = ref();
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  if(formData.examineFileList.length >= 1){
    proxy.$modal.msgError(`只能上传一个文件!`);
    return;
  }
  const imageType = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/svg",
    "application/pdf",
  ];
  const isImage = imageType.includes(file.type ? file.type : file[0].type);
  //检验文件格式
  if (!isImage) {
    proxy.$modal.msgError(`文件格式错误!`);
    return false;
  }
  // 文件大小限制（20MB）
  const isValidSize = file.size ? file.size : file[0].size <= 20 * 1024 * 1024; // 20MB
  if (!isValidSize) {
    proxy.$modal.msgError(`文件大小超过限制（最大20MB）`);
    return false;
  }
  return true;
}

function doCancelBuy() {
  showBuyDialogFlag.value = false;
}

function doBuy() {
  showBuyDialogFlag.value = false;
}

function doCollect() {
  isCollectedFlag.value = !isCollectedFlag.value;
  if (getToken()) {
    if (isCollectedFlag.value) {
      addDtbBookCollect(bookIdDecrypt.value).then((res) => {
        if (res.code === 200) {
        }
      });
    } else {
      delDtbBookCollect(bookIdDecrypt.value).then((res) => {
        if (res.code === 200) {
        }
      });
    }
  } else {
    ElNotification({
      title: "操作提示",
      message: "请先登录",
      type: "success",
    });
    // 跳转登录页
    proxy.$router.push({ path: "/login" });
  }
}

function toNaturalResourcesPage(currentBook) {
  if (getToken()) {
    const isPay =
      currentBook.addWay !== null && !currentBook.expireDate ? 0 : 1;
    proxy.$router.push({
      path: "/book-natural-resources",
      query: { bookId: currentBook.bookId, isPay: isPay },
    });
  } else {
    // 跳转登录页
    proxy.$router.push({ path: "/login" });
  }
}

async function applicationForUse() {
  if (getToken()) {
    const resSiteConfig = await getSiteConfig(1);
    if (resSiteConfig.code == 200) {
      siteConfigInfo.value = resSiteConfig.data;
      console.log(siteConfigInfo.value);
    }
    if (userInfo.value?.userType === "2") {
      if (currentBook.value.shelfState === 1) {
        trialDialogVisible.value = true;
      } else {
        ElNotification({
          title: "操作提示",
          message: "该教材未上架",
          type: "warning",
        });
      }
    } else {
      promptDialogVisible.value = true;
    }
  } else {
    // 跳转登录页
    proxy.$router.push({ path: "/login" });
  }
}
const formRef = ref();
const trialDialogClose = () => {
  trialDialogVisible.value = false;
  formRef.value.resetFields();
};
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      formData.bookId = bookIdDecrypt.value;
      formData.trialDays = siteConfigInfo.value.trialDayLimit;
      const fileList = [];
      submitTrialApplicationEducation(formData).then((res) => {
        if (res.code === 200) {
          formData.examineFileList.forEach((element, index) => {
            const parm = {
              bookId: bookIdDecrypt.value,
              fileName: element.name,
              fileUrl: element.response.url,
              fileType: element.name.substring(
                element.name.lastIndexOf(".") + 1
              ),
              fileSize: element.size,
              businessId: res.data,
            };
            fileList.push(parm);
          });
          addFile(fileList);
          ElNotification({
            title: "操作提示",
            message: res.msg,
            type: "success",
          });
          trialDialogVisible.value = false;
        } else {
          ElNotification({
            title: "操作提示",
            message: res.msg,
            type: "error",
          });
        }
      });
    } else {
      // 校验失败，提示用户
      ElNotification({
        title: "操作提示",
        message: "请完善表单内容",
        type: "warning",
      });
      return false;
    }
  });
}

function cancelDialog() {
  trialDialogVisible.value = false;
  formData.applicationReason = "";
  formData.examineFileList = "";
}

function changExpandAllFlag(data) {
  expandAllFlagData.value = !expandAllFlagData.value;
}

function showBuyPopup() {
  if (getToken()) {
    showBuyDialogFlag.value = true;
  } else {
    // 跳转登录页
    proxy.$router.push({ path: "/login" });
  }
}

async function exchange() {
  if (getToken()) {
    router.push({ path: "/book-codes" });
  } else {
    // 跳转登录页
    proxy.$router.push({ path: "/login" });
  }
}

// 鼠标按下事件
function onMouseDown(event) {
  mouseDown.value = true;
  initialMouseX.value = event.clientX; // 获取鼠标按下时的横坐标
  initialWidth.value = catalogWidth.value; // 获取初始宽度
  document.addEventListener("mousemove", onMouseMove); // 监听鼠标移动
  document.addEventListener("mouseup", onMouseUp); // 监听鼠标松开
}

// 鼠标移动事件
function onMouseMove(event) {
  if (mouseDown.value) {
    const deltaX = event.clientX - initialMouseX.value; // 计算鼠标移动的距离
    const newWidth = catalogWidth.value + -deltaX; // 计算新的宽度
    setTimeout(() => {
      catalogWidth.value = Math.max(newWidth, 100); // 确保宽度最小为100
    }, 500);
  }
}

// 鼠标松开事件
function onMouseUp() {
  mouseDown.value = false;
  document.removeEventListener("mousemove", onMouseMove.value); // 移除鼠标移动事件
  document.removeEventListener("mouseup", onMouseUp.value); // 移除鼠标松开事件
}

const doToSearchPage = (queryCriteria) => {
  // 查询
  router.push({
    path: "/search",
    query: {
      ...queryCriteria,
    },
  });
};

function getEditorsChoice() {
  getRecommendedTextbooks({ bookId: bookIdDecrypt.value }).then((res) => {
    if (res.code === 200) {
      editorsChoice.value = res.data[0];
    }
  });
}

function getListChapter() {
  queryBookChapterListByBookDetail({ bookId: bookIdDecrypt.value }).then(
    (res) => {
      if (res.code === 200) {
        debugger
        if (getToken()) {
          if (
            currentBook.value.addWay !== null &&
            !currentBook.value.expireDate
          ) {
            res.data.forEach((element) => {
              element.free = 2;
              element.children.forEach((item) => {
                item.free = 2;
              });
            });
          }
        }
        chapterData.value = res.data;
      }
    }
  );
}

function moreRecommendations() {
  router.push({
    path: "/search",
    query: {
      searchKey: searchTerm.value,
    },
  });
}

async function supportingTextbooks(item) {
  masterFlag.value = false;
  await queryBookChapterListByBookDetail({ bookId: item.bookId }).then(
    (res) => {
      if (res.code === 200) {
        if (getToken()) {
          if (
            currentBook.value.addWay !== null &&
            !currentBook.value.expireDate
          ) {
            res.data.forEach((element) => {
              element.free = 2;
              element.children.forEach((item) => {
                item.free = 2;
              });
            });
          }
          //  Object.assign(chapterData.value, res.data);
        }
        chapterData.value = res.data;
      }
    }
  );
}

function goStudy() {
  gotoReader(bookIdDecrypt.value);
}

const coverClick = async () => {
  reloadKey.value++;
  isShow.value = false;
  masterFlag.value = true;
  await getListChapter();
  try {
    if (getToken()) {
      const parm = {
        userId: userInfo.value?.userId,
        bookId: bookIdDecrypt.value,
      };
      const response = await listCollect(parm);
      if (response.code === 200 && response.rows.length > 0) {
        isCollectedFlag.value = true;
      }
    }
    const res = await getDtbBookInfo({
      bookId: bookIdDecrypt.value,
      userId: userInfo.value ? userInfo.value?.userId : 0,
    });
    // 在获取currentBook.value后的逻辑中添加排序逻辑
    if (assistantBookIdDecrypt.value && res.data.deputyBookList?.length) {
      const index = res.data.deputyBookList.findIndex(
        (item) => item.bookId === assistantBookIdDecrypt.value.data
      );

      if (index > -1) {
        // 创建新数组避免直接修改原数组
        const sortedList = [
          res.data.deputyBookList[index],
          ...res.data.deputyBookList.slice(0, index),
          ...res.data.deputyBookList.slice(index + 1),
        ];
        // 使用Vue的响应式方法更新数组
        res.data.deputyBookList = [...sortedList];
      }
    }
    if (res.data.deputyBookList?.length > 0) {
      currentBookcount.value = res.data.deputyBookList.length;
    } else {
      res.data.deputyBookList = null;
    }
    // 初始化 visibleBooks
    if (res.data.deputyBookList?.length > 10) {
      visibleBooks.value = res.data.deputyBookList.slice(0, 10);
    } else {
      visibleBooks.value = res.data.deputyBookList;
    }
    currentBook.value = res.data; // 假设返回的数据结构是 { data: {} }
    searchTerm.value = res.data.bookName;
    await getEditorsChoice();
  } catch (error) {
    console.error("获取教材详情失败", error);
  }
};
const handleUpdate = async (bookId) => {
  isCollectedFlag.value = false;
  bookIdDecrypt.value = bookId;
  // 调用书籍封面点击方法触发数据刷新
  await coverClick();
  reloadKey.value++;
};
const isShow = ref(false);
onMounted(async () => {
  bookIdDecrypt.value = bookId;
  assistantBookIdDecrypt.value = assistantBookId;
  try {
    if (getToken()) {
      const res = await userStore.getInfo();
      userInfo.value = res.user;
      const parm = {
        userId: userInfo.value?.userId,
        bookId: bookIdDecrypt.value,
      };
      if (res.user) {
        await addPrint({ bookId: bookIdDecrypt.value });
      }
      const response = await listCollect(parm);
      if (response.code === 200 && response.rows.length > 0) {
        isCollectedFlag.value = true;
      }
    }
    const res = await getDtbBookInfo({
      bookId: bookIdDecrypt.value,
      userId: userInfo ? userInfo.userId : 0,
    });
    if (res.data == undefined) {
      ElNotification({
        title: "操作提示",
        message: "该教材不存在或已下架！",
        type: "error",
      });
      proxy.$router.push({ path: "/index" });
    }
    else if (res.data && res.data.shelfState === 2) {
      ElNotification({
        title: "操作提示",
        message: "该教材未上架！",
        type: "error",
      });
      proxy.$router.push({ path: "/index" });
    } else if (res.data && res.data.shelfState === 3) {
      ElNotification({
        title: "操作提示",
        message: "该教材已被召回！",
        type: "error",
      });
      proxy.$router.push({ path: "/index" });
    }
    if (
      res.data.topSubjectId ||
      res.data.secondSubjectId ||
      res.data.thirdSubjectId ||
      res.data.forthSubjectId
    ) {
      //获取专业，生成层次面包屑
      // prepareTo2ForSbujectName(res.data);
      bookItem.value = res.data;
    }
    // 在获取currentBook.value后的逻辑中添加排序逻辑
    if (assistantBookIdDecrypt.value && res.data.deputyBookList?.length > 0) {
      const index = res.data.deputyBookList.findIndex(
        (item) => item.bookId === assistantBookIdDecrypt.value
      );

      if (index > -1) {
        // 创建新数组避免直接修改原数组
        const sortedList = [
          res.data.deputyBookList[index],
          ...res.data.deputyBookList.slice(0, index),
          ...res.data.deputyBookList.slice(index + 1),
        ];
        // 使用Vue的响应式方法更新数组
        res.data.deputyBookList = [...sortedList];
      }
    }
    if (res.data.deputyBookList?.length > 0) {
      currentBookcount.value = res.data.deputyBookList.length;
    } else {
      res.data.deputyBookList = null;
    }
    // 初始化 visibleBooks
    if (res.data.deputyBookList?.length > 10) {
      visibleBooks.value = res.data.deputyBookList.slice(0, 10);
    } else {
      visibleBooks.value = res.data.deputyBookList;
    }
    currentBook.value = res.data; // 假设返回的数据结构是 { data: {} }
    searchTerm.value = res.data.bookName;
    await getEditorsChoice();
    if (assistantBookId) {
      const param = {
        bookId: assistantBookId,
      };
      isShow.value = true;
      await supportingTextbooks(param);
    } else {
      await getListChapter();
      // 取消副教材的光标选中状态
      isShow.value = false;
    }
  } catch (error) {
    console.error("获取教材详情失败", error);
  }
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index.scss";

.book-detail-con {
  width: 100%;
  min-height: 100vh;
  @extend .base-flex-column;
  justify-content: flex-start;
  aligin-items: center;
  margin-bottom: 10px;

  .search-con {
    width: 100%;
    color: #c8bbe7;
  }

  .data-detail-con {
    width: 100%;
    margin-bottom: 43px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;

    .type-level-con {
      @extend .base-comp-card-per;
      height: 28px;
      text-align: left;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      line-height: 28px;
      margin-bottom: 31px;
    }

    .data-info-con {
      margin-bottom: 68px;
      @extend .base-comp-card-per;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;

      .data-info-left {
        width: 371px;
        margin-right: 71px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: center;

        .book-info-con {
          width: 100%;
          margin-bottom: 10px;
          position: relative;
          cursor: pointer;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: center;

          .book-img {
            width: 266px;
            height: 371px;
            margin-bottom: -10px;
            z-index: 12;
          }

          .book-img:hover {
            opacity: 0.9;
          }

          .line-top {
            width: 371px;
            height: 23px;
            border-radius: 2px;
            background: linear-gradient(180deg, #f8f8f8 0%, #efefef 100%);
          }

          .line-middle {
            width: 371px;
            height: 16px;
            border-radius: 2px;
            background: #ffffff;
            z-index: 12;
          }

          .line-bottom {
            width: 376px;
            height: 22px;
            background: #d2d2d2;
            filter: blur(9px);
            margin-top: -10px;
          }
        }

        .btn-con {
          margin-bottom: 26px;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;

          .collection-btn {
            width: 150px;
            height: 50px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #cbcaca;
            margin-right: 16px;
            @extend .base-flex-row;
            justify-content: center;
            align-items: center;

            .icon-img {
              width: 22px;
              height: 22px;
              margin-right: 14px;
            }

            .icon-text {
              min-width: 32px;
              height: 28px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 600;
              font-size: 16px;
              color: #333333;
              line-height: 28px;
              text-align: center;
              margin-bottom: 2px;
            }
          }

          .collection-btn:hover {
            opacity: 0.8;
          }

          .source-btn {
            width: 150px;
            height: 50px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #0966b4;
            @extend .base-flex-row;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            .icon-img {
              width: 20px;
              height: 20px;
              margin-right: 14px;
            }

            .icon-text {
              width: 48px;
              height: 28px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 600;
              font-size: 16px;
              color: #0966b4;
              line-height: 28px;
              text-align: center;
              font-style: normal;
            }
          }

          .source-btn:hover {
            background: rgba(9, 102, 180, 0.08);
          }
        }

        .purchase-notes {
          width: 100%;
          height: 46px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 46px;
          text-align: left;
          font-style: normal;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;

          .purchase-notes-title {
            width: 58px;
            height: 46px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 46px;
            text-align: left;
            font-style: normal;
          }

          .purchase-notes-content {
            width: 328px;
            height: 46px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .data-info-right {
        flex: 1;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;
        .right-top-con {
          min-height: 194px;
          margin-bottom: 26px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: flex-start;

          .info-text-con {
            .right-title {
              width: 100%;
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #333333;
              line-height: 46px;
              text-align: left;
              font-style: normal;
            }

            .authors-info {
              width: 100%;
              height: 38px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              line-height: 38px;
              text-align: left;
              margin-bottom: 56px;
            }

            .item-info-con {
              width: 100%;

              @extend .base-flex-row;
              justify-content: flex-start;
              align-items: stretch;
              .item-row-con {
                @extend .base-flex-column;
                justify-content: flex-start;
                align-items: flex-start;
                .item-col {
                  @extend .base-flex-row;
                  justify-content: flex-start;
                  align-items: flex-start;
                  .label {
                    min-width: 70px;
                    text-align: left;
                    font-style: normal;
                    font-family:
                      PingFangSC,
                      PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                  }
                  .text {
                    min-width: 146px;
                    min-height: 38px;
                    font-family:
                      PingFangSC,
                      PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    text-align: left;
                  }
                }
              }
            }
          }

          .info-orc-con {
            width: 156px;

            .orc-item {
              @extend .base-flex-column;
              justify-content: flex-start;
              align-items: center;

              .orc-top {
                width: 156px;
                height: 156px;
                background: linear-gradient(270deg, #bbedff 0%, #a0c7ff 100%);
                border-radius: 12px 12px 0px 0px;
                @extend .base-flex-row;
                justify-content: center;
                align-items: center;

                .orc-img {
                  width: 123px;
                  height: 123px;
                }
              }

              .orc-bottom {
                width: 100%;
                height: 46px;
                background: #ffffff;
                box-shadow: 0px 4px 8px 0px #e4e4e4;
                border-radius: 12px;
                filter: blur(0px);
                @extend .base-flex-row;
                justify-content: center;
                align-items: center;

                .phone-icon {
                  width: 10px;
                  height: 8px;
                  margin-right: 8px;
                }

                .phone-text {
                  width: 56px;
                  height: 46px;
                  font-family:
                    PingFangSC,
                    PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  line-height: 46px;
                  text-align: center;
                }
              }
            }
          }
        }

        .right-middle-con {
          width: 100%;
          height: 94px;
          background: #f7f7f7;
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: center;

          .price-info-con {
            margin-left: 28px;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;

            .sale-price {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 16px;
              color: #999999;
              line-height: 46px;
              text-align: left;
              margin-right: 31px;

              span {
                min-width: 149px;
                height: 46px;
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 600;
                font-size: 24px;
                color: #0966b4;
                line-height: 46px;
                text-align: left;
                font-style: normal;
              }
            }

            .fix-price {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 16px;
              color: #999999;
              line-height: 46px;
              text-align: left;
              font-style: normal;
              text-decoration-line: line-through;
            }
            .fix-price-shelfState {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 16px;
              color: #999999;
              line-height: 46px;
              text-align: left;
              font-style: normal;
            }
          }

          .read-num-con {
            margin-right: 59px;
            @extend .base-flex-row;
            justify-content: flex-end;
            align-items: center;

            .read-num-icon {
              width: 29px;
              height: 15px;
            }

            span {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 16px;
              color: #666666;
              line-height: 46px;
              text-align: left;
              font-style: normal;
            }
          }
        }

        .right-bottom-con {
          width: 100%;
          margin-bottom: 38px;

          .supporting-textbooks-con {
            width: 100%;
            @extend .base-flex-column;
            justify-content: flex-start;
            align-items: flex-start;

            .title {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 500;
              font-size: 18px;
              color: #333333;
              line-height: 46px;
              text-align: left;
            }

            .book-list {
              width: 100%;
              min-height: 100px;
              @extend .base-flex-row;
              justify-content: space-between;
              align-items: center;

              .list-mv-btn {
                width: 31px;
                height: 100px;
                background: #f7f7f7;
                border-radius: 4px;
                border: 1px solid #e5e6e7;
                text-align: center;
                line-height: 85px;
                color: #666666;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
              }

              .list-mv-btn:last-child {
                margin: 0px;
              }

              .list-mv-btn:hover {
                opacity: 0.8;
                background: #f0f1f1;
              }

              .book-item-con {
                flex: 1;
                @extend .base-flex-row;
                justify-content: space-around;
                align-items: center;

                .book-item {
                  align-self: flex-end;

                  .book-img-con {
                    cursor: pointer;

                    .book-img {
                      width: 72px;
                      height: 100px;
                      border-radius: 4px;
                    }
                  }

                  .book-img-con:hover {
                    opacity: 0.8;
                  }
                }
              }
            }
          }
        }

        .right-bottom-btn-con {
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;
          width: 100%;

          .btn-blue {
            width: 180px;
            height: 50px;
            background: #0966b4;
            border-radius: 8px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            line-height: 50px;
            text-align: center;
            margin-right: 18px;
            cursor: pointer;
          }

          .btn-blue:hover {
            opacity: 0.8;
          }

          .btn-white {
            width: 180px;
            height: 50px;
            background: #ffffff;
            border: 1px solid #d7d8d8;
            border-radius: 8px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #666666;
            line-height: 50px;
            text-align: center;
            cursor: pointer;
          }

          .btn-white:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .introduction-catalog-con {
      margin-bottom: 43px;
      @extend .base-comp-card-per;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: stretch;

      .introduction-con {
        width: 60%;
        min-height: 500px;
        background: #ffffff;
        border: 1px solid #e5e6e7;
        margin-right: 21px;
        transition:
          opacity 0.5s ease,
          visibility 0.5s ease;
        /* 设置过渡效果 */
        border-radius: 2px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;

        .head-title {
          width: 100%;
          height: 66px;
          background: linear-gradient(180deg, #e9f3ff 0%, #f0fbff 100%);
          border-bottom: 1px solid #e5e6e7;
          padding-left: 32px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: flex-end;

          .title {
            height: 48px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #0966b4;
            line-height: 46px;
            text-align: left;
            text-align: center;
            border-bottom: 4px solid #0966b4;
          }
        }

        .introduction-text {
          padding: 32px 48px 48px 32px;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;

          .label {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 46px;
            text-align: left;
            margin-bottom: 10px;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;
          }

          .i-content {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 22px;
            text-align: left;
          }
        }
      }

      .introduction-con.show {
        animation: fadeIn 0.3s forwards;
      }

      .introduction-con.hide {
        animation: fadeOut 0.3s forwards;
      }

      @keyframes fadeIn {
        0% {
          opacity: 0.8;
          visibility: visible;
          /* 必须可见 */
        }

        50% {
          opacity: 0.9;
        }

        100% {
          opacity: 1;
        }
      }

      @keyframes fadeOut {
        0% {
          opacity: 0.8;
        }

        50% {
          opacity: 0.7;
        }

        100% {
          opacity: 0;
          visibility: hidden;
          /* 动画结束后隐藏 */
        }
      }

      .catalog-con {
        flex: 1;
        border: 1px solid #e5e6e7;
        border-radius: 2px;
        position: relative;
        transition: all 0.3s ease-in-out;

        .left-btn {
          position: absolute;
          top: 45%;
          left: 0;
          width: 16px;
          height: 96px;
          background: #fcfcfc;
          border-radius: 0px 16px 16px 0px;
          border: 1px solid #e5e6e7;
          border-left: 0px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: flex-end;
          align-items: center;

          .right-arrow {
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 8px solid #c1c1c1;
            margin-right: 2px;
          }

          .left-arrow {
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 8px solid #c1c1c1;
          }
        }

        .right-arrow,
        .left-btn:hover {
          opacity: 0.8;
          background-color: #f4f4f5;
        }

        .head-title {
          width: 100%;
          background: linear-gradient(180deg, #e9f3ff 0%, #f0fbff 100%);
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: center;

          .title-left {
            min-width: 40px;
            height: 66px;
            line-height: 66px;
            text-align: center;
            border-bottom: 1px solid #e5e6e7;
            padding-left: 30px;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: flex-end;

            .title {
              height: 48px;
              line-height: 48px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 600;
              font-size: 20px;
              color: #0966b4;
              line-height: 48px;
              text-align: center;
              border-bottom: 4px solid #0966b4;
            }
          }

          .title-right {
            margin-right: 24px;

            .title-btn {
              height: 46px;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              line-height: 46px;
              text-align: right;
              cursor: pointer;
            }

            .title-btn:hover {
              color: #0966b4;
            }
          }
        }

        .catalog-tree-list-con {
          flex: 1;
          padding-top: 30px;
          padding-left: 20px;
        }
      }
    }

    .related-textbooks-con {
      @extend .base-comp-card-per;
      @extend .base-flex-row;
      justify-content: center;
      align-items: flex-start;

      .related-textbooks {
        width: 100%;

        .title-con {
          width: 100%;
          border-bottom: 3px solid;
          border-image: linear-gradient(
              224deg,
              rgba(21, 159, 219, 1),
              rgba(9, 102, 180, 1)
            )
            3 3;
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: center;

          .title {
            width: 162px;
            height: 54px;
            background: #0966b4;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #ffffff;
            line-height: 54px;
            text-align: center;
          }

          .more-btn {
            height: 46px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #0966b4;
            line-height: 46px;
            text-align: center;
            cursor: pointer;
          }

          .more-btn:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .buy-comp-con {
      width: 100%;
      height: 200px;
      border-radius: 8px;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;

      .info-con {
        width: 85%;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;

        .title {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          text-align: left;
          margin-bottom: 20px;
        }

        .name {
          width: 100%;
          margin-bottom: 20px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;

          .label {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            text-align: left;
            margin-right: 10px;
          }

          .info {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 300;
            font-size: 16px;
            color: #7a8796ff;
            text-align: left;
          }
        }

        .price {
          margin-bottom: 40px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;

          .label {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            text-align: left;
            margin-right: 10px;
          }

          .ori-price {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 300;
            font-size: 16px;
            color: #7a8796ff;
            text-align: left;
            margin-right: 10px;
            text-decoration: line-through;
          }

          .sell-price {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 300;
            font-size: 16px;
            color: #7a8796ff;
            text-align: left;
          }
        }
      }

      .btn-con {
        width: 90%;
        @extend .base-flex-row;
        justify-content: space-between;
        align-items: center;

        .cancel-btn {
          width: 115px;
          height: 42px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          border-radius: 8px;
          text-align: center;
          line-height: 42px;
          background-color: #eaeeefff;
          color: #3665a3;
          cursor: pointer;
        }

        .cancel-btn:hover {
          opacity: 0.8;
        }

        .buy-btn {
          width: 280px;
          height: 42px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          border-radius: 8px;
          background-color: #2354a1ff;
          color: #ffffff;
          text-align: center;
          line-height: 42px;
          cursor: pointer;
        }

        .buy-btn:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.book-item {
  display: inline-block;
  margin: 5px;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.book-item.active .book-img {
  border: 3px solid #007bff;
  /* 高亮的边框颜色 */
  transform: scale(1.1);
  /* 高亮时稍微放大 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.book-img {
  width: 100px;
  height: 150px;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.book-img.hover {
  transform: scale(1.2);
  /* 鼠标悬停时放大 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.list-mv-btn {
  cursor: pointer;
  font-size: 20px;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  user-select: none;
  transition: background-color 0.3s;
}

.list-mv-btn:hover {
  background-color: #007bff;
  color: #fff;
}
.custom-file-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

.custom-file-item .el-image {
  margin-right: 8px;
  cursor: pointer;
}
</style>
