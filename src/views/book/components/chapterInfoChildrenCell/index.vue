<template>
  <div class="chapter-info-children-cell">
    <!-- 主要内容行 -->
    <div class="cell-main-row">
      <div class="ch-cell-left" @click="gotoReaderCell">
        <el-icon class="item-lock-icon-ch"><Lock v-if="item.free === 1" /></el-icon>
        <div class="label-ch">{{ item.chapterName || item.name }}</div>
      </div>
      <div class="ch-cell-right" v-if="item.children && item.children.length > 0">
        <div class="right-icon-con" @click="toggleChildren">
          <el-icon class="right-icon" v-if="showChildren">
            <ArrowUp />
          </el-icon>
          <el-icon class="right-icon" v-else>
            <ArrowDown />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 递归渲染子级 -->
    <div
      class="children-container"
      v-if="item.children && item.children.length > 0 && showChildren"
    >
      <chapterInfoChildrenCell
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        :expandAllFlag="expandAllFlag"
        @click="$emit('click', child)"
        @showBuyDialog="$emit('showBuyDialog', $event)"
      />
    </div>
  </div>
</template>

<script setup name="ChapterInfoChildrenCell">
import { ElMessageBox, ElMessage } from "element-plus";
import { getShopBook } from "@/api/shop/book";
import { getToken } from "@/utils/auth";

const router = useRouter();

const props = defineProps({
  item: {
    type: Object,
    default: () => ({
      name: '',
      children: [],
    }),
  },
  level: {
    type: Number,
    default: 1,
  },
  expandAllFlag: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['click', 'showBuyDialog']);

const showChildren = ref(false);

// 监听 expandAllFlag 变化
watch(
  () => props.expandAllFlag,
  (newValue) => {
    showChildren.value = newValue;
  },
  { immediate: true }
);

// 切换子级显示状态
function toggleChildren() {
  showChildren.value = !showChildren.value;
}

// 点击跳转到阅读器
function gotoReaderCell() {
  // 如果有catalogId，说明是页面级别的内容
  if (props.item.catalogId) {
    if (props.item.free === 2) {
      window.open(
        `/reader?k=${props.item.bookId}&cid=${props.item.chapterId}&cataid=${props.item.catalogId}`,
        "_blank"
      );
      sessionStorage.setItem("chapterId", props.item.chapterId);
    } else {
      ElMessageBox.confirm("当前章节收费，无法阅读。是否确认购买", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          if (getToken()) {
            await getShopBook({ bookId: props.item.bookId }).then((res) => {
              if (res.code === 200) {
                // 这里可以触发购买弹窗
                emit('showBuyDialog', res.data);
              }
            });
          } else {
            router.push({ path: "/login" });
          }
        })
        .catch(() => {
          ElMessage({
            type: "info",
            message: "已取消购买",
          });
        });
    }
  } else if (props.item.children && props.item.children.length > 0) {
    // 如果有子级，则切换展开状态
    toggleChildren();
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.chapter-info-children-cell {
  width: 100%;
  min-height: 44px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: flex-start;

  // 主要内容行
  .cell-main-row {
    width: 100%;
    min-height: 44px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    &:hover {
      background-color: #f0f0f0;
      border-radius: 4px;
    }
  }

  .ch-cell-left {
    flex: 1;
    @extend .base-flex-row;
    justify-content: flex-start;
    align-items: center;
    padding: 8px 12px;

    .item-lock-icon-ch {
      width: 14px;
      height: 16px;
      margin-right: 10px;
      color: #999;
    }

    .label-ch {
      flex: 1;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
    }
  }

  .ch-cell-right {
    width: auto;
    padding-right: 12px;

    .right-icon-con {
      width: 20px;
      height: 20px;
      background: #e8e8e8;
      border-radius: 3px;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        background: #d0d0d0;
      }

      .right-icon {
        width: 12px;
        height: 12px;
        color: #666;
      }
    }
  }

  // 子级容器
  .children-container {
    width: 100%;
    padding-left: 20px; // 每一级增加20px缩进
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;

    // 为更深层级添加更多缩进
    .chapter-info-children-cell {
      .children-container {
        padding-left: 20px;
      }
    }
  }
}
</style>
