<template>
  <div class="banner-sty">
    <el-carousel :interval="5000" style="height: 100%" arrow="hover" v-if="refresh">
      <el-carousel-item v-for="(item, index) in banners" :key="index" class="img-carousel-item">
        <img class="img-sty" :src="item.imageUrl" @click="jumpBanner(item)" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup name="Banner">
const show = ref(false);
import { getBanners } from '@/api/basic/home.js';
// import { BANNER_KEY } from '@/utils/constant.js';
const banners = ref([]);
const refresh = ref(true);
function doGetBanners() {
  getBanners({ bannerPosition: 1, device: 'PC' })
    .then((res) => {
      if (res.code == 200) {
        banners.value = res.data;
        refresh.value = !refresh.value;
        setTimeout(() => {
          refresh.value = true;
        }, 100)
        // sessionStorage.setItem(BANNER_KEY, JSON.stringify(banners.value));
      }
    })
    .catch((error) => {});
}
function jumpBanner(item) {
  if (item.linkUrl) {
    window.open(item.linkUrl);
  }
}
onMounted(() => {
  // banners.value = JSON.parse(sessionStorage.getItem(BANNER_KEY));
  // if (!banners.value) {

  // }
});
doGetBanners()

</script>

<style lang="scss" scoped>
.banner-sty {
  width: 100%;
  height: 442px;
  z-index: 2;
  .img-carousel-item {
    width: 100%;
    height: 442px;
  }
  .img-sty {
    width: 100%;
    height: 100%;
  }
}
</style>
