<template>
  <div
    class="book-share-reader-wrapper __reader__"
    :reader-theme="store.theme"
    rer="reader"
  >
    <Header
      v-if="operationFlag"
      :messageTag="false"
      :show-message-hint="false"
      :simple-preview-mode="true"
      :show-reading-progress="false"
      :show-ai-assistent="false"
      :show-book-lines="false"
      :show-book-marks="false"
      :show-return-to-home="false"
      :showTourGuide="false"
      :show-search="false"
    />
    <div class="reader-main">
      <div class="content">
        <Menus
          v-if="operationFlag"
          ref="menusRef"
          :showRelatedBooks="false"
          :show-knowledge-graph="false"
        />
        <!-- __hidden-scroll__     隐藏滚动条样式 -->
        <main
          class="content-pages"
          ref="pageContentContainer"
          :style="store.styleSetting"
          :id="PAGE_ITEMS_CONTAINER_ID"
          @click.stop="mouseup"
          @mousedown.stop="mousedown"
        >
          <reflowable-layout
            v-if="store.pageFlippingMethod === 'y'"
            :has-book-mark="false"
          />
          <folio-simulation
            v-else-if="store.pageFlippingMethod === 'r'"
            :has-book-mark="false"
          />
        </main>
        <ShareComment @edit="editNote" :book-id="bookId" :share-id="shareId" ref="toolRef" />
      </div>
      <div
          class="SelectionBox"
          v-if="selectionTableBar.isShow"
      >
        <SelectionBookShare
            :x="selectionTableBar.x"
            :y="selectionTableBar.y"
            :elementText="selectionTableBar.selectedElementText"
            :elementList="selectionTableBar.elementList"
            :elementIdList="selectionTableBar.elementIdList"
            :close="closeSelectionBox"
            :isStatus="selectionTableBar.isShow"
            :book-id="bookId"
            :share-id="shareId"
        />
      </div>
      <AddComment
          v-if="noteIsShow"
          :isShow="noteIsShow"
          :setIsShow="setIsShow"
          :elementIdList="selectionTableBar.elementIdList"
          :elementText="selectionTableBar.selectedElementText"
          :editNoteData="editNoteData"
          @cancel="noteIsShow = false"
          @confirm="getBookNoteList"
          :chapter-id="onSelectedPageNodeData.chapterId"
          :page-index-in-chapter="onSelectedPageNodeData.pageIndexInChapter"
          :book-id="bookId"
          :share-id="shareId"
      />
      <imagePreview></imagePreview>
      <PreviewDialog />
    </div>

    <el-dialog
        v-model="showShareCode"
        style="margin-top: 30vh !important"
        width="500"
        @close="showShareCode = false"
    >
      <template #header>
      <span>
        分享码
      </span>
      </template>
      <el-row style="margin-bottom: 20px">
        <el-col :span="24">
          <el-input
              v-model="shareCode"
              placeholder="请输入分享码"
          />
        </el-col>
      </el-row>
      <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleShareCodeCheck">
          确认
        </el-button>
      </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import Header from "./sub/Header.vue";
import useReader, {READING_MODE} from '@/store/modules/reader';
import {onMounted, ref, watch, onBeforeUnmount, getCurrentInstance, provide, nextTick, reactive} from 'vue';
import Menus from './sub/Menus.vue'
import imagePreview from '@/components/galleryImagePreview/index.vue'
import ReflowableLayout from './sub/Pages/ReflowableLayout.vue'
import FolioSimulation from './sub/Pages/FolioSimulation.vue'
import PreviewDialog from './sub/Tool/PreviewDialog.vue'
import {
  PAGE_ITEMS_CONTAINER_ID,
  generatePaidPageContent,
  hasAccessToReadChapter,
  PAGE_ITEMS_CSS_CLASS,
  BOOK_PAGE_INDEX,
  PAGE_TURNING_ERROR_MAPPER,
  highlightKeyWordSmooth,
  TEXT_SELECTION_TOOLBAR_ID, findClosestPageNode, PAGE_CHAPTER_ID_ATTR, CHAPTER_PAGE_NUMBER,
} from "@/utils/reader";
import {
  bookCopyText,
  checkShareCode,
  getShareComment,
  getChaptersSimple,
  getChaptersSimpleBook,
  isShareCode
} from "@/api/book/reader";
import {ElInput, ElMessage, ElMessageBox} from "element-plus";
import {throttle} from "lodash";
import useAutoReading from "@/store/modules/autoReading.js";
// import printJS from "print-js";
import ShareComment from "@/views/reader/sub/ShareComment.vue";
import SelectionBookShare from "@/views/reader/sub/Tool/SelectionBookShare.vue";
let noteIsShow = ref(false);
const editNoteData = ref({});
const setIsShow = (v) => (noteIsShow.value = v);
import {copyText, uuid} from "@/utils/index.js";
import AddComment from "@/views/reader/sub/Tool/AddShareComment.vue";
// import print from 'print-js'

provide("simplifiedReadingMode", true);


const { proxy } = getCurrentInstance();
const store = useReader();
const shareId = ref();
const showShareCode = ref(false);
const showShareFlag = ref(false);
const shareCode = ref('');
const bookId = ref();
const chapterId = ref();
let selectionList = [];
const start = reactive({ x: 0, y: 0 });
const selectionTableBar = reactive({
  selectedElementText: '',
  isShow: false,
  x: 0,
  y: 0,
  elementList: [],
  elementIdList: []
});
let onSelectedPageNodeData = ref({
  pageIndexInChapter: undefined,
  pageIndexInBook: undefined,
  chapterId: undefined,
});
const props = defineProps({
  initChapterId: {
    type: String,
    default: "",
  },
  initCataId: {
    type: String,
    default: "",
  },
});
const menusRef = ref(null);
const operationFlag = ref(true);
const pageContentContainer = ref("pageContentContainer");
watch(
  () => store.styleSetting["line-height"],
  (newVal, oldVal) => {
    var element = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
    var pTags = element.querySelectorAll("p");
    // console.log(pTags)
    pTags.forEach(function (pTag) {
      pTag.style.lineHeight = store.styleSetting["line-height"];
    });
  },
  { deep: true }
);

/**在Note组件调用AddNote组件*/
const editNote = (type, val) => {
  if (type === "note") {
    editNoteData.value = val;
    noteIsShow.value = true;
  }
};

window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SCROLL_TO_ELEMENT') {
    const domId = event.data.payload.domId;
    let targetHeadingDom = document.querySelector(
        `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${domId}"]`
    );

    // 节头跳转问题修改
    if (!targetHeadingDom) {
      targetHeadingDom = document.querySelector(
          `div[id="${domId}"]`
      );
    }
    if (targetHeadingDom) {
      highlightKeyWordSmooth(targetHeadingDom);
    }
  }
}, false);

// window.addEventListener('message', function (event) {
//   // 可选：校验 origin 安全性
//   if (event.data.type === 'PRINT') {
//     // printJS(PAGE_ITEMS_CONTAINER_ID,'html');
//     const container = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
//     const canvases = container.querySelectorAll('canvas');
//     const canvasArray = Array.from(canvases);
//
//     // 移除 canvas
//     canvasArray.forEach(canvas => canvas.remove());
//     printJS({
//       printable: PAGE_ITEMS_CONTAINER_ID, // 元素 ID
//       type: 'html',
//       scanStyles: false, // 避免样式丢失
//       ignoreElements: ['canvas'], // 忽略所有 canvas 元素
//       targetStyles: ['*'],
//       onPrintDialogClose: () => {
//         // 打印完成后恢复 canvas
//         canvasArray.forEach(canvas => container.appendChild(canvas));
//       }
//     });
//
//   }
// });

function getBookNoteList() {
  noteIsShow.value = false;
  getShareComment({
    bookId: bookId.value,
    sort: 1,
    noteType: 1,
  }).then((res) => {
    if (res.code === 200) store.setNoteList(res.data);
  });
}

// 检查是否设置分享码
async function  getIsShareCode() {
  let res = await isShareCode(shareId.value);
  showShareCode.value = res.data;
  if (!res.data){
    initReader();
  }
}

// 验证分享码
async function handleShareCodeCheck() {
  if (shareCode.value) {
    let param = {
      "shareId":shareId.value,
      "code":shareCode.value
    }
    let res = await checkShareCode(param);
    if (!res.data.checkFlag){
      ElMessage.error("验证失败,请重新输入");
    }else{
      showShareCode.value = false;
      bookId.value = res.data.bookId;
      ElMessage.success("验证成功");
      initReader()
    }
  } else {
    ElMessage.error("请输入分享码");
  }
}

function initReader() {
  getChapterList().then(() => {
    store.setSimplifiedMode(true);
    store.jumpToChapter(chapterId.value, 1);
  });
  document.title = "章节预览";
  nextTick(() => {
    pageContentContainer.value.addEventListener("scroll", scrollCallback);
    pageContentContainer.value.addEventListener("wheel", wheelCallback);
  });
}

const getSelectedText = () => {
  return selectionList.map((ele) => ele.innerText).join('')
};

onMounted(() => {
  shareId.value = proxy.$route.query.shareId;
  getIsShareCode();
});

const closeSelectionBox = (type) => {
  const selectedText = getSelectedText();
  const textNode = selectionTableBar.elementList[0];
  const selectedNodeId = textNode.getAttribute("id");
  const selectedNode = document.querySelector(
      `#${PAGE_ITEMS_CONTAINER_ID} [id="${selectedNodeId}"]`
  );
  const closestPageNode = findClosestPageNode(selectedNode);
  onSelectedPageNodeData = {
    chapterId: closestPageNode?.getAttribute(PAGE_CHAPTER_ID_ATTR),
    pageIndexInChapter: closestPageNode?.getAttribute(CHAPTER_PAGE_NUMBER),
    pageIndexInBook: closestPageNode?.getAttribute(BOOK_PAGE_INDEX),
  };
  switch (type) {
    case "note":
      editNoteData.value = {};
      noteIsShow.value = true;
      break;
    default:
      break;
  }
  selectionTableBar.isShow = false;
  selectionList = [];
};

const mouseup = (e) => {
  function getSelectedNodes() {
    const selectedNodes = [];
    // 获取当前选中对象
    const selection = window.getSelection();
    if (!selection.isCollapsed && selection.rangeCount > 0) {
      // 通过 range 对象获取选中文本的开始和结束节点
      const range = selection.getRangeAt(0);
      // 创建一个 DocumentFragment，为循环遍历范围中的节点准备
      const fragment = range.cloneContents();

      // 获取文本节点
      const startTextNode = range.startContainer;
      const endTextNode = range.endContainer;

      // 如果仅仅选中了一个字
      if (startTextNode === endTextNode) {
        // 获取文本节点的上一级节点
        const parentNode = startTextNode.parentNode;
        selectedNodes.push(parentNode.cloneNode(false));
        return selectedNodes;
      }

      // 递归遍历并收集所有节点
      function collectNodes(node) {
        if (node.classList?.contains('Page-item-header') || node.classList?.contains('Page-item-footer')) {
          crossPageSelection = true;
        }
        if (crossPageSelection) {
          return
        }
        if (node?.classList?.contains?.('reader-text') && node?.getAttribute?.('sign') === 'selection') {
          selectedNodes.push(node);
        }
        node.childNodes.forEach(collectNodes);
      }
      fragment.childNodes.forEach(collectNodes);
    }
    if (crossPageSelection) {
      return []
    }
    // selection.isCollapsed情况下，selectedNodes的长度是0
    return selectedNodes;
  }

  let crossPageSelection = false;

  if (crossPageSelection) {
    // 如果进行了跨page选取文字, 则不进行任何处理
    selectionTableBar.isShow = false;
    return;
  }

  // 这里添加timeout是因为click事件会在mouseup事件之前触发，所以需要等待click事件处理完后再进行处理
  setTimeout(() => {
    selectionList = getSelectedNodes();
    if (selectionList.length > 300) {
      ElMessage.error("笔记划线超过300字，请重新选择");
      selectionList = [];
      selectionTableBar.elementList = [];
      selectionTableBar.elementIdList = [];
      return;
    }

    selectionTableBar.x = Math.abs(start.x - e.clientX) / 2 + (e.clientX <= start.x ? e.clientX : start.x);
    selectionTableBar.y = (e.clientY <= start.y ? e.clientY : start.y) - 10;
    selectionTableBar.elementList = selectionList;
    selectionTableBar.elementIdList = selectionList.map(item => item.getAttribute('id'));
    selectionTableBar.selectedElementText = getSelectedText();
    if (selectionList.length <= 300 && selectionList.length > 0) {
      selectionTableBar.isShow = true;
    }
  }, 0);
};

const mousedown = (e) => {
  const selectionToolbar = e.target.closest('#' + TEXT_SELECTION_TOOLBAR_ID);
  if (!selectionToolbar) {
    // 如果没有点击文字自定义工具条，则隐藏工具条
    selectionTableBar.isShow = false;
  }
  start.x = e.clientX;
  start.y = e.clientY;
};
function calculatePreviousPageHeight(allPageNodes, currentIndexInTheNodeArray) {
  let heightInTotal = 0;
  for (let i = currentIndexInTheNodeArray; i > -1; i--) {
    heightInTotal += allPageNodes[i].offsetHeight;
  }
  return heightInTotal;
}
function hit2Top(scrollTopValue) {
  if (scrollTopValue === 0) {
    return true;
  }
  return false;
}
function hit2Bottom(
    pageIndexInContainer,
    pageTotalInView,
    containerHeight,
    remainingVisibleHeightOfCurrentPageInContainer
) {
  // console.log('判断是否到底页面底部：', pageIndexInContainer === (pageTotalInView - 1), Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight))
  if (
      pageIndexInContainer === pageTotalInView - 1 &&
      Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight) <
      80
  ) {
    return true;
  }
  return false;
}

const autoReadingStore = useAutoReading();
let loadNewChapterTimer = null;
let cumulatedHitTimesOnWill = 0;
let need2Jump2NextChapter = false;
let need2Jump2PreviousChapter = false;
let loadingChapterInProgress = false;
let pageScrollDirection = "";
function autoReadingLoadChapter(nextChapterOrPreviousChapter) {
  clearTimeout(loadNewChapterTimer);
  if (store.reading === "automaticReading") {
    loadNewChapterTimer = setTimeout(() => {
      if (nextChapterOrPreviousChapter === "next") {
        if (!store.isLastChapter()) {
          ElMessage.info("即将进入下一章");
        }
        store.nextPage().catch((error) => {
          if (error.code === PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.code) {
            autoReadingStore.pause();
            ElMessage.info("您已阅读到教材最后一页，5秒后将退出自动阅读");
            setTimeout(() => {
              store.setReading(READING_MODE.GENERAL);
            }, 5000);
          }
          return error;
        });
      } else if (nextChapterOrPreviousChapter === "previous") {
        // console.log('去往上一页')
        store.lastPage();
      }
    }, 2000);
  }
}
const scrollCallback = throttle(
    () => {
      // console.log('scrollCallback')
      // 真实阅读模式，不需要计算当前滑动到第几页, 也不会触发这里scroll事件
      const pageContainer = document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`);
      const containerHeight = pageContainer.offsetHeight;
      const pagesDom = document.querySelectorAll(
          `#${PAGE_ITEMS_CONTAINER_ID} .${PAGE_ITEMS_CSS_CLASS}`
      );
      let scrollTopValue = pageContainer.scrollTop;
      let targetPage = null;

      // 自动阅读模式只能向下浏览，不会向上浏览
      if (hit2Top(scrollTopValue) && store.reading !== "automaticReading") {
        need2Jump2PreviousChapter = true;
        need2Jump2NextChapter = !need2Jump2PreviousChapter;
        autoReadingLoadChapter("previous");
        // console.log('hit2top { need2Jump2PreviousChapter }:', need2Jump2PreviousChapter)
      }
      // 当页面滑动到最底部或最顶部的时候，该事件不会被继续触发, 所以仅在scroll事件触发的时候会将cumulatedHitTimesOnWill次数设置为0
      cumulatedHitTimesOnWill = 0;
      for (
          let i = 0,
              len = pagesDom.length,
              remainedVisibleHeightOfCurrentPageInContainer;
          i < len;
          i++
      ) {
        const cumulatedPreviousPageHeight = calculatePreviousPageHeight(
            pagesDom,
            i - 1
        );
        const remainingScrollValue =
            scrollTopValue - cumulatedPreviousPageHeight - pagesDom[i].offsetHeight;
        if (remainingScrollValue > 0) {
          continue;
        }
        remainedVisibleHeightOfCurrentPageInContainer =
            cumulatedPreviousPageHeight + pagesDom[i].offsetHeight - scrollTopValue;
        if (remainedVisibleHeightOfCurrentPageInContainer >= containerHeight) {
          // 当前页面占据整个容器
          targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
          // break
        } else if (
            remainedVisibleHeightOfCurrentPageInContainer < containerHeight &&
            remainedVisibleHeightOfCurrentPageInContainer > containerHeight / 2
        ) {
          // 当前页面占据整个容器的二分之一以上
          targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
          // break
        } else if (
            remainedVisibleHeightOfCurrentPageInContainer <
            containerHeight / 2
        ) {
          // 当前页面占据整个容器不到二分之一
          if (i < len) {
            targetPage = pagesDom[i + 1].getAttribute(BOOK_PAGE_INDEX);
            // break
          }
          break;
        }
        if (
            hit2Bottom(
                i,
                len,
                containerHeight,
                remainedVisibleHeightOfCurrentPageInContainer
            )
        ) {
          need2Jump2NextChapter = true;
          need2Jump2PreviousChapter = !need2Jump2NextChapter;
          autoReadingLoadChapter("next");
          // console.log('hit2bottom { need2Jump2NextChapter}:', need2Jump2NextChapter)
        }
        if (targetPage != null) {
          break;
        }
      }
      if (targetPage != null) {
        // 阅读器在自动阅读状态下切换翻页方式，由于使用了节流throttle，所以这个scrollCallback会有延迟, 会导致targetPage是null，最优化的方式是修改当前页码的判断
        store.setCurrentPageIndex(targetPage);
      }
    },
    400,
    {
      trailing: true,
    }
);

const wheelCallback = throttle(
    (event) => {
      if (loadingChapterInProgress) {
        promptUserWithChapterLoading();
        return;
      }
      // 自动阅读模式下，鼠标滚轮事件不会被触发
      // console.log('wheelcallback主动触发')
      if (event.deltaY < 0) {
        pageScrollDirection = "up";
      } else {
        pageScrollDirection = "down";
      }
      if (need2Jump2PreviousChapter && pageScrollDirection === "up") {
        cumulatedHitTimesOnWill++;
        // console.log('下滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
        if (cumulatedHitTimesOnWill > 2) {
          cumulatedHitTimesOnWill = 0;
          loadingChapterInProgress = true;
          return store
              .lastPage()
              .catch((error) => {
                if (
                    error.type === PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.code
                ) {
                  ElMessage.warning(
                      PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.msg
                  );
                }
                return error;
              })
              .finally(() => {
                loadingChapterInProgress = false;
              });
        }
      } else if (need2Jump2NextChapter && pageScrollDirection === "down") {
        cumulatedHitTimesOnWill++;
        // console.log('上滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
        if (cumulatedHitTimesOnWill > 2) {
          cumulatedHitTimesOnWill = 0;
          loadingChapterInProgress = true;
          return store.nextPage().finally(() => {
            loadingChapterInProgress = false;
          });
        }
      }
    },
    200,
    {
      trailing: true,
    }
);

watch(
    () => store.pageFlippingMethod,
    (nValue) => {
      if (nValue === "r") {
        // 真实分页后不通过鼠标滚轮事件更新当前第几页
        pageContentContainer.value.removeEventListener("scroll", scrollCallback);
        pageContentContainer.value.removeEventListener("wheel", wheelCallback);
      } else {
        nextTick(() => {
          pageContentContainer.value.addEventListener("scroll", scrollCallback);
          pageContentContainer.value.addEventListener("wheel", wheelCallback);
        });
      }
    }
);

onBeforeUnmount(() => {
  ElMessageBox.close();
  store.deauthorizeToTheBook();
});


// 递归处理章节数据的函数（分享阅读器版）
function processChapterDataRecursivelyShare(chapterInfo, pagesCnt = { count: 0 }, itemType = 'chapter') {
  // 设置基本属性
  chapterInfo.catalogId = chapterInfo.catalogId || chapterInfo.chapterId;
  chapterInfo.title = chapterInfo.title || chapterInfo.chapterName || chapterInfo.name || "";

  // 添加类型标识字段
  chapterInfo.itemType = itemType;

  // 检查访问权限
  chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo);

  // 处理子级数据
  if (chapterInfo.children && Array.isArray(chapterInfo.children) && chapterInfo.children.length > 0) {
    // 检查子级中是否都是 catalog 类型
    const allChildrenAreCatalogs = chapterInfo.children.every(child =>
      child.catalogId && child.domId && !child.chapterTotalPages
    );

    if (allChildrenAreCatalogs && itemType === 'chapter') {
      // 子级都是 catalog，当前是最后一级的 chapter
      chapterInfo.children = chapterInfo.children.map(child => {
        return processChapterDataRecursivelyShare(child, pagesCnt, 'catalog');
      });

      // 最后一级的 chapter，计算页数
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
        ? 1
        : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
        ? chapterTotalPages
        : 1;

      // 计入总页数
      pagesCnt.count += chapterTotalPages;
      console.log(`最后一级章节: ${chapterInfo.title}, 类型: ${itemType}, 页数: ${chapterTotalPages}`);

    } else {
      // 子级中有 chapter，继续递归
      chapterInfo.children = chapterInfo.children.map(child => {
        const childType = child.catalogId && child.domId ? 'catalog' : 'chapter';
        return processChapterDataRecursivelyShare(child, pagesCnt, childType);
      });

      // 容器章节本身不包含页面内容
      chapterInfo.totalPages = 0;
      console.log(`容器章节: ${chapterInfo.title}, 类型: ${itemType}, 子级数量: ${chapterInfo.children.length}`);
    }

  } else if (chapterInfo.catalogs && Array.isArray(chapterInfo.catalogs) && chapterInfo.catalogs.length > 0) {
    // 如果使用的是 catalogs 字段，转换为 children
    chapterInfo.children = chapterInfo.catalogs.map(child => {
      return processChapterDataRecursivelyShare(child, pagesCnt, 'catalog');
    });
    delete chapterInfo.catalogs;

    // 有 catalogs 的章节是最后一级的 chapter
    if (itemType === 'chapter') {
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
        ? 1
        : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
        ? chapterTotalPages
        : 1;

      // 计入总页数
      pagesCnt.count += chapterTotalPages;
      console.log(`包含catalogs的最后一级章节: ${chapterInfo.title}, 类型: ${itemType}, 页数: ${chapterTotalPages}`);
    } else {
      chapterInfo.totalPages = 0;
    }

  } else {
    // 没有子级的项目
    chapterInfo.children = chapterInfo.children || [];

    if (itemType === 'catalog') {
      // catalog 类型：是标题，不计入页数
      chapterInfo.totalPages = 0;
      console.log(`章节标题: ${chapterInfo.title}, 类型: ${itemType}, 不计入页数`);
    } else {
      // 没有子级的 chapter - 这是最后一级章节，应该有内容
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1"; // 默认为1页而不是0
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
        ? 1
        : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
        ? chapterTotalPages
        : 1;

      // 最后一级章节都应该计入页数
      pagesCnt.count += chapterTotalPages;
      console.log(`最后一级内容章节: ${chapterInfo.title}, 类型: ${itemType}, 页数: ${chapterTotalPages}`);
    }
  }

  // 如果没有访问权限，生成付费页面内容
  if (!chapterInfo.hasAccessToChapter) {
    chapterInfo.chapterContent = generatePaidPageContent();
  }

  return chapterInfo;
}

// 递归查找第一个有内容的章节
function findFirstContentChapter(chapters) {
  for (let chapter of chapters) {
    if (chapter.itemType === 'chapter' && chapter.totalPages > 0) {
      return chapter;
    }
    if (chapter.children && chapter.children.length > 0) {
      const found = findFirstContentChapter(chapter.children);
      if (found) return found;
    }
  }
  return null;
}

// 加载目录信息
function getChapterList() {
  let chapterInfoArray = [];
  return getChaptersSimpleBook(shareId.value).then((res) => {
    if (res.code === 200) {
      chapterInfoArray = res.data || [];

      let pagesCnt = { count: 0 };

      // 递归处理所有章节数据
      chapterInfoArray = chapterInfoArray.map(chapterInfo => {
        return processChapterDataRecursivelyShare(chapterInfo, pagesCnt, 'chapter');
      });

      // 查找第一个有内容的章节作为默认章节
      const firstContentChapter = findFirstContentChapter(chapterInfoArray);
      if (firstContentChapter) {
        chapterId.value = firstContentChapter.chapterId;
      } else if (chapterInfoArray.length > 0) {
        chapterId.value = chapterInfoArray[0].chapterId;
      }

      // 设置所有章节的信息
      store.setChaptersData(chapterInfoArray, pagesCnt.count);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  });
}
</script>
<style lang="scss" scoped>
@import "./sub/readerTheme.scss";
.__reader__ {
  width: 100vw;
  height: 100vh;
  background-color: var(--pageBackgroundColor);
  .reader-main {
    overflow: hidden;
    width: 100%;
    height: calc(100% - 48px);
  }
}
.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  .content-pages {
    width: 100%;
    height: 100%;
    min-height: 800px;
    overflow-y: auto;
  }
}
.simplified-reader-wrapper {
  .Bookmark {
    display: none;
  }
}
</style>
