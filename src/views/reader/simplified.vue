<template>
  <div
      class="simplified-reader-wrapper __reader__"
      :reader-theme="store.theme"
      ref="reader"
      @contextmenu.prevent
      @copy.prevent>
    <Header
        v-if="operationFlag"
        :messageTag="false"
        :show-message-hint="false"
        :simple-preview-mode="true"
        :show-reading-progress="false"
        :show-ai-assistent="false"
        :show-book-lines="false"
        :show-book-marks="false"
        :show-return-to-home="false"
        :showTourGuide="false"
        :show-search="false"
    />
    <div class="reader-main">
      <div class="content">
        <Menus
            v-if="operationFlag"
            ref="menusRef"
            :showRelatedBooks="false"
            :show-knowledge-graph="false"
        />
        <!-- __hidden-scroll__     隐藏滚动条样式 -->
        <main
            class="content-pages"
            ref="pageContentContainer"
            :style="store.styleSetting"
            :id="PAGE_ITEMS_CONTAINER_ID"
        >
          <reflowable-layout
              v-if="store.pageFlippingMethod === 'y'"
              :has-book-mark="false"
          />
          <folio-simulation
              v-else-if="store.pageFlippingMethod === 'r'"
              :has-book-mark="false"
          />
        </main>
      </div>
      <imagePreview></imagePreview>
      <PreviewDialog/>
    </div>

  </div>
</template>

<script setup>
import Header from "./sub/Header.vue";
import useReader, {READING_MODE} from '@/store/modules/reader';
import {onMounted, ref, watch, onBeforeUnmount, getCurrentInstance, provide, nextTick} from 'vue';
import Menus from './sub/Menus.vue';
import imagePreview from '@/components/galleryImagePreview/index.vue';
import ReflowableLayout from './sub/Pages/ReflowableLayout.vue';
import FolioSimulation from './sub/Pages/FolioSimulation.vue';
import PreviewDialog from './sub/Tool/PreviewDialog.vue';
import {
  PAGE_ITEMS_CONTAINER_ID,
  generatePaidPageContent,
  hasAccessToReadChapter, PAGE_ITEMS_CSS_CLASS, BOOK_PAGE_INDEX, PAGE_TURNING_ERROR_MAPPER, highlightKeyWordSmooth,
} from "@/utils/reader";
import {getBookByRedisToken, getChaptersSimple} from "@/api/book/reader";
import {ElMessage, ElMessageBox} from "element-plus";
import {throttle} from "lodash";
import useAutoReading from "@/store/modules/autoReading.js";
import {listenForJumpRequests} from "@/utils/windowStoreSync.js";
// import print from 'print-js'

import {
  setupPostMessageListener,
  sendPageLoadedMessage,
  sendContentLoadedMessage,
  sendErrorMessage,
  sendUserInteractionMessage,
  getConnectionStatus,
  setupGlobalErrorHandling,
  handleScrollToElement,
  handlePrintRequest,
  handleInitData
} from '@/utils/postMessageHandler.js';

provide("simplifiedReadingMode", true);
const {proxy} = getCurrentInstance();
const store = useReader();
const props = defineProps({
  initChapterId: {
    type: String,
    default: "",
  },
  initCataId: {
    type: String,
    default: "",
  },
});
const menusRef = ref(null);

// 将函数定义移到顶层
// 递归处理章节数据的函数（简化版）
function processChapterDataRecursivelySimple(chapterInfo, pagesCnt = {count: 0}, itemType = 'chapter') {
  // 设置基本属性
  chapterInfo.catalogId = chapterInfo.catalogId || chapterInfo.chapterId;
  chapterInfo.title = chapterInfo.title || chapterInfo.chapterName || chapterInfo.name || "";

  // 添加类型标识字段
  chapterInfo.itemType = itemType;

  // 检查访问权限
  chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo);

  // 处理子级数据
  if (chapterInfo.children && Array.isArray(chapterInfo.children) && chapterInfo.children.length > 0) {
    // 检查子级中是否都是 catalog 类型
    const allChildrenAreCatalogs = chapterInfo.children.every(child =>
        child.catalogId && child.domId && !child.chapterTotalPages
    );

    if (allChildrenAreCatalogs && itemType === 'chapter') {
      // 子级都是 catalog，当前是最后一级的 chapter
      chapterInfo.children = chapterInfo.children.map(child => {
        return processChapterDataRecursivelySimple(child, pagesCnt, 'catalog');
      });

      // 最后一级的 chapter，计算页数
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1;

      // 计入总页数
      pagesCnt.count += chapterTotalPages;

    } else {
      // 子级中有 chapter，继续递归
      chapterInfo.children = chapterInfo.children.map(child => {
        const childType = child.catalogId && child.domId ? 'catalog' : 'chapter';
        return processChapterDataRecursivelySimple(child, pagesCnt, childType);
      });

      // 容器章节本身不包含页面内容
      chapterInfo.totalPages = 0;
    }

  } else if (chapterInfo.catalogs && Array.isArray(chapterInfo.catalogs) && chapterInfo.catalogs.length > 0) {
    // 如果使用的是 catalogs 字段，转换为 children
    chapterInfo.children = chapterInfo.catalogs.map(child => {
      return processChapterDataRecursivelySimple(child, pagesCnt, 'catalog');
    });
    delete chapterInfo.catalogs;

    // 有 catalogs 的章节是最后一级的 chapter
    if (itemType === 'chapter') {
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1;

      // 计入总页数
      pagesCnt.count += chapterTotalPages;
    } else {
      chapterInfo.totalPages = 0;
    }

  } else {
    // 没有子级的项目
    chapterInfo.children = chapterInfo.children || [];

    if (itemType === 'catalog') {
      // catalog 类型：是标题，不计入页数
      chapterInfo.totalPages = 0;
    } else {
      // 没有子级的 chapter - 这是最后一级章节，应该有内容
      let chapterTotalPages = chapterInfo.chapterTotalPages || "1"; // 默认为1页而不是0
      chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages);

      chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1;

      // 最后一级章节都应该计入页数
      pagesCnt.count += chapterTotalPages;
    }
  }

  // 如果没有访问权限，生成付费页面内容
  if (!chapterInfo.hasAccessToChapter) {
    chapterInfo.chapterContent = generatePaidPageContent();
  }

  return chapterInfo;
}

function getChapterList(bookId, chapterId, fromType) {
  let chapterInfoArray = [];
  if (!fromType) {
    fromType = 1;
  }
  return getChaptersSimple(bookId, fromType).then((res) => {
    if (res.code === 200) {
      chapterInfoArray = res.data || [];
      console.log('简化版阅读器原始章节数据:', chapterInfoArray);
      console.log('查找的 chapterId:', chapterId);

      // 如果指定了 chapterId，则递归查找对应的章节
      if (chapterId) {
        // 递归查找目标章节
        function findChapterRecursively(chapters, targetChapterId) {
          for (let chapter of chapters) {
            console.log(`检查章节: ${chapter.chapterId} (${chapter.name || chapter.title})`);
            if (chapter.chapterId === targetChapterId) {
              return chapter;
            }
            if (chapter.children && chapter.children.length > 0) {
              const found = findChapterRecursively(chapter.children, targetChapterId);
              if (found) return found;
            }
          }
          return null;
        }

        const targetChapter = findChapterRecursively(chapterInfoArray, chapterId);
        if (targetChapter) {
          console.log(`找到目标章节: ${targetChapter.name} (${targetChapter.chapterId})`);
          chapterInfoArray = [targetChapter]; // 只保留找到的章节
        } else {
          console.error(`未找到 chapterId: ${chapterId} 对应的章节`);
          console.error('可用的章节ID列表:', chapterInfoArray.map(c => ({
            chapterId: c.chapterId,
            name: c.name || c.title
          })));
          chapterInfoArray = []; // 如果找不到，返回空数组
        }
      }

      console.log('简化版阅读器章节数据:', chapterInfoArray);
      let pagesCnt = {count: 0};

      // 递归处理所有章节数据
      chapterInfoArray = chapterInfoArray.map(chapterInfo => {
        return processChapterDataRecursivelySimple(chapterInfo, pagesCnt, 'chapter');
      });

      // 设置所有章节的信息
      store.setChaptersData(chapterInfoArray, pagesCnt.count);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  });
}

const operationFlag = ref(true);
const pageContentContainer = ref("pageContentContainer");

// PostMessage 相关状态
const connectionStatus = ref({isConnected: false, parentOrigin: null});
const receivedMessageCount = ref(0);
const sentMessageCount = ref(0);

watch(
    () => store.styleSetting["line-height"],
    (newVal, oldVal) => {
      var element = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
      var pTags = element.querySelectorAll("p");
      // console.log(pTags)
      pTags.forEach(function (pTag) {
        pTag.style.lineHeight = store.styleSetting["line-height"];
      });
    },
    {deep: true}
);

// PostMessage 处理函数
const postMessageHandlers = {
  handleScrollToElement: (data) => {
    receivedMessageCount.value++;
    handleScrollToElement(data, highlightKeyWordSmooth);
  },

  handlePrintRequest: () => {
    receivedMessageCount.value++;
    handlePrintRequest(PAGE_ITEMS_CONTAINER_ID);
  },

  handleInitData: (data) => {
    receivedMessageCount.value++;
    handleInitData(data);
  },

  handleUpdateContent: (data) => {
    receivedMessageCount.value++;
    console.log('处理内容更新请求:', data);
    // 在这里添加内容更新逻辑
  }
};

// 更新连接状态
function updateConnectionStatus() {
  connectionStatus.value = getConnectionStatus();
}

// 禁用打印功能
const handleKeyDown = (event) => {
  // 禁用 Ctrl+P 打印
  if (event.ctrlKey && event.key === 'p') {
    event.preventDefault();
    console.log('Ctrl+P 打印功能已被禁用');
    return false;
  }

  if (event.ctrlKey && event.shiftKey && event.key === 'P') {
    event.preventDefault();
    console.log('Ctrl+Shift+P 打印功能已被禁用');
    return false;
  }
};

window.addEventListener('message', function (event) {
  // 可选：校验 origin 安全性
  if (event.data.type === 'PRINT') {
    // printJS(PAGE_ITEMS_CONTAINER_ID,'html');
    const container = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
    const canvases = container.querySelectorAll('canvas');
    const canvasArray = Array.from(canvases);

    // 禁用 Ctrl+Shift+I 开发者工具
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
      event.preventDefault();
      console.log('Ctrl+Shift+I 开发者工具已被禁用');
      return false;
    }

    // 禁用 Ctrl+U 查看源代码
    if (event.ctrlKey && event.key === 'u') {
      event.preventDefault();
      console.log('查看源代码功能已被禁用');
      return false;
    }
  }

  async function getBookByToken(redisToken) {
    try {
      const response = await getBookByRedisToken(redisToken);
      if (response.code !== 200) {
        throw new Error(response.msg || "链接无效");
      }
      return response.data; // { bookId, chapterId }
    } catch (error) {
      // 发送错误消息给父窗口
      sendErrorMessage(error.message || "链接无效");

      // 直接跳转到错误页面
      proxy.$router.push('/error/token-expired');
      return null;
    }
  }

  const mousedown = (e) => {
    start.x = e.clientX;
    start.y = e.clientY;
  };

  function calculatePreviousPageHeight(allPageNodes, currentIndexInTheNodeArray) {
    let heightInTotal = 0;
    for (let i = currentIndexInTheNodeArray; i > -1; i--) {
      heightInTotal += allPageNodes[i].offsetHeight;
    }
    return heightInTotal;
  }

  function hit2Top(scrollTopValue) {
    if (scrollTopValue === 0) {
      return true;
    }
    return false;
  }

  function hit2Bottom(
      pageIndexInContainer,
      pageTotalInView,
      containerHeight,
      remainingVisibleHeightOfCurrentPageInContainer
  ) {
    // console.log('判断是否到底页面底部：', pageIndexInContainer === (pageTotalInView - 1), Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight))
    if (
        pageIndexInContainer === pageTotalInView - 1 &&
        Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight) <
        80
    ) {
      return true;
    }
    return false;
  }

  const autoReadingStore = useAutoReading();
  let loadNewChapterTimer = null;
  let cumulatedHitTimesOnWill = 0;
  let need2Jump2NextChapter = false;
  let need2Jump2PreviousChapter = false;
  let loadingChapterInProgress = false;
  let pageScrollDirection = "";

  function autoReadingLoadChapter(nextChapterOrPreviousChapter) {
    clearTimeout(loadNewChapterTimer);
    if (store.reading === "automaticReading") {
      loadNewChapterTimer = setTimeout(() => {
        if (nextChapterOrPreviousChapter === "next") {
          if (!store.isLastChapter()) {
            ElMessage.info("即将进入下一章");
          }
          store.nextPage().catch((error) => {
            if (error.code === PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.code) {
              autoReadingStore.pause();
              ElMessage.info("您已阅读到教材最后一页，5秒后将退出自动阅读");
              setTimeout(() => {
                store.setReading(READING_MODE.GENERAL);
              }, 5000);
            }
            return error;
          });
        } else if (nextChapterOrPreviousChapter === "previous") {
          // console.log('去往上一页')
          store.lastPage();
        }
      }, 2000);
    }
  }

  const scrollCallback = throttle(
      () => {
        // console.log('scrollCallback')
        // 真实阅读模式，不需要计算当前滑动到第几页, 也不会触发这里scroll事件
        const pageContainer = document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`);
        const containerHeight = pageContainer.offsetHeight;
        const pagesDom = document.querySelectorAll(
            `#${PAGE_ITEMS_CONTAINER_ID} .${PAGE_ITEMS_CSS_CLASS}`
        );
        let scrollTopValue = pageContainer.scrollTop;
        let targetPage = null;

        // 自动阅读模式只能向下浏览，不会向上浏览
        if (hit2Top(scrollTopValue) && store.reading !== "automaticReading") {
          need2Jump2PreviousChapter = true;
          need2Jump2NextChapter = !need2Jump2PreviousChapter;
          autoReadingLoadChapter("previous");
          // console.log('hit2top { need2Jump2PreviousChapter }:', need2Jump2PreviousChapter)
        }
        // 当页面滑动到最底部或最顶部的时候，该事件不会被继续触发, 所以仅在scroll事件触发的时候会将cumulatedHitTimesOnWill次数设置为0
        cumulatedHitTimesOnWill = 0;
        for (
            let i = 0,
                len = pagesDom.length,
                remainedVisibleHeightOfCurrentPageInContainer;
            i < len;
            i++
        ) {
          // 当前页内容仍然占据整个容器高度
          // console.log('data value:', scrollTopValue, pagesDom[i].offsetHeight, containerHeight)
          const cumulatedPreviousPageHeight = calculatePreviousPageHeight(
              pagesDom,
              i - 1
          );
          const remainingScrollValue =
              scrollTopValue - cumulatedPreviousPageHeight - pagesDom[i].offsetHeight;
          if (remainingScrollValue > 0) {
            // 当前node完全消失在容器内, 则继续计算
            // scrollTopValue = remainingScrollValue
            continue;
          }
          remainedVisibleHeightOfCurrentPageInContainer =
              cumulatedPreviousPageHeight + pagesDom[i].offsetHeight - scrollTopValue;
          if (remainedVisibleHeightOfCurrentPageInContainer >= containerHeight) {
            // 当前页面占据整个容器
            targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
            // break
          } else if (
              remainedVisibleHeightOfCurrentPageInContainer < containerHeight &&
              remainedVisibleHeightOfCurrentPageInContainer > containerHeight / 2
          ) {
            // 当前页面占据整个容器的二分之一以上
            targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
            // break
          } else if (
              remainedVisibleHeightOfCurrentPageInContainer <
              containerHeight / 2
          ) {
            // 当前页面占据整个容器不到二分之一
            if (i < len) {
              targetPage = pagesDom[i + 1].getAttribute(BOOK_PAGE_INDEX);
              // break
            } else {
              // 按理说不应该进入这个分支, 需要检查页面显示效果是否正确
              // debugger
            }
            break;
          } else {
            // 按理说不应该进入这个分支,当前页面不可能占据页面不到二分之一，又大于整个容器
            // debugger
            // if (i === (len - 1) && remainedVisibleHeightOfCurrentPageInContainer <= 100) {
            //   targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX)
            //   // 加载下一章
            // }
          }
          // pageDom的长度和store.pageData的长度一致
          if (
              hit2Bottom(
                  i,
                  len,
                  containerHeight,
                  remainedVisibleHeightOfCurrentPageInContainer
              )
          ) {
            need2Jump2NextChapter = true;
            need2Jump2PreviousChapter = !need2Jump2NextChapter;
            autoReadingLoadChapter("next");
            // console.log('hit2bottom { need2Jump2NextChapter}:', need2Jump2NextChapter)
          }
          if (targetPage != null) {
            break;
          }
        }
        if (targetPage != null) {
          // 阅读器在自动阅读状态下切换翻页方式，由于使用了节流throttle，所以这个scrollCallback会有延迟, 会导致targetPage是null，最优化的方式是修改当前页码的判断
          store.setCurrentPageIndex(targetPage);
        }
      },
      400,
      {
        trailing: true,
      }
  );

  const wheelCallback = throttle(
      (event) => {
        if (loadingChapterInProgress) {
          promptUserWithChapterLoading();
          return;
        }
        // 自动阅读模式下，鼠标滚轮事件不会被触发
        // console.log('wheelcallback主动触发')
        if (event.deltaY < 0) {
          pageScrollDirection = "up";
        } else {
          pageScrollDirection = "down";
        }
        if (need2Jump2PreviousChapter && pageScrollDirection === "up") {
          cumulatedHitTimesOnWill++;
          // console.log('下滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
          if (cumulatedHitTimesOnWill > 2) {
            cumulatedHitTimesOnWill = 0;
            loadingChapterInProgress = true;
            return store
                .lastPage()
                .catch((error) => {
                  if (
                      error.type === PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.code
                  ) {
                    ElMessage.warning(
                        PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.msg
                    );
                  }
                  return error;
                })
                .finally(() => {
                  loadingChapterInProgress = false;
                });
          }
        } else if (need2Jump2NextChapter && pageScrollDirection === "down") {
          cumulatedHitTimesOnWill++;
          // console.log('上滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
          if (cumulatedHitTimesOnWill > 2) {
            cumulatedHitTimesOnWill = 0;
            loadingChapterInProgress = true;
            return store.nextPage().finally(() => {
              loadingChapterInProgress = false;
            });
          }
        }
      },
      200,
      {
        trailing: true,
      }
  );

  watch(
      () => store.pageFlippingMethod,
      (nValue) => {
        if (nValue === "r") {
          // 真实分页后不通过鼠标滚轮事件更新当前第几页
          pageContentContainer.value.removeEventListener("scroll", scrollCallback);
          pageContentContainer.value.removeEventListener("wheel", wheelCallback);
        } else {
          nextTick(() => {
            pageContentContainer.value.addEventListener("scroll", scrollCallback);
            pageContentContainer.value.addEventListener("wheel", wheelCallback);
          });
        }
      }
  );

// 监听来自子窗口的跳转请求
  const cleanupJumpListener = listenForJumpRequests(store);


  onBeforeUnmount(() => {
    ElMessageBox.close();
    store.deauthorizeToTheBook();
    cleanupJumpListener();
  });

  // 递归处理章节数据的函数（简化版）
  function processChapterDataRecursivelySimple(chapterInfo, pagesCnt = {count: 0}, itemType = 'chapter') {
    // 设置基本属性
    chapterInfo.catalogId = chapterInfo.catalogId || chapterInfo.chapterId;
    chapterInfo.title = chapterInfo.title || chapterInfo.chapterName || chapterInfo.name || "";

    // 添加类型标识字段
    chapterInfo.itemType = itemType;

    // 检查访问权限
    chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo);

    // 处理子级数据
    if (chapterInfo.children && Array.isArray(chapterInfo.children) && chapterInfo.children.length > 0) {
      // 检查子级中是否都是 catalog 类型
      const allChildrenAreCatalogs = chapterInfo.children.every(child =>
          child.catalogId && child.domId && !child.chapterTotalPages
      );

      if (allChildrenAreCatalogs && itemType === 'chapter') {
        // 子级都是 catalog，当前是最后一级的 chapter
        chapterInfo.children = chapterInfo.children.map(child => {
          return processChapterDataRecursivelySimple(child, pagesCnt, 'catalog');
        });

        // 最后一级的 chapter，计算页数
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
            ? 1
            : Number(chapterTotalPages);

        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
            ? chapterTotalPages
            : 1;

        // 计入总页数
        pagesCnt.count += chapterTotalPages;

      } else {
        // 子级中有 chapter，继续递归
        chapterInfo.children = chapterInfo.children.map(child => {
          const childType = child.catalogId && child.domId ? 'catalog' : 'chapter';
          return processChapterDataRecursivelySimple(child, pagesCnt, childType);
        });

        // 容器章节本身不包含页面内容
        chapterInfo.totalPages = 0;
      }

    } else if (chapterInfo.catalogs && Array.isArray(chapterInfo.catalogs) && chapterInfo.catalogs.length > 0) {
      // 如果使用的是 catalogs 字段，转换为 children
      chapterInfo.children = chapterInfo.catalogs.map(child => {
        return processChapterDataRecursivelySimple(child, pagesCnt, 'catalog');
      });
      delete chapterInfo.catalogs;

      // 有 catalogs 的章节是最后一级的 chapter
      if (itemType === 'chapter') {
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
            ? 1
            : Number(chapterTotalPages);

        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
            ? chapterTotalPages
            : 1;

        // 计入总页数
        pagesCnt.count += chapterTotalPages;
      } else {
        chapterInfo.totalPages = 0;
      }

    } else {
      // 没有子级的项目
      chapterInfo.children = chapterInfo.children || [];

      if (itemType === 'catalog') {
        // catalog 类型：是标题，不计入页数
        chapterInfo.totalPages = 0;
      } else {
        // 没有子级的 chapter（可能是空章节）
        let chapterTotalPages = chapterInfo.chapterTotalPages || "0";
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
            ? 0
            : Number(chapterTotalPages);

        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
            ? chapterTotalPages
            : (chapterTotalPages > 0 ? 1 : 0);

        if (chapterTotalPages > 0) {
          pagesCnt.count += chapterTotalPages;
        }
      }
    }

    // 如果没有访问权限，生成付费页面内容
    if (!chapterInfo.hasAccessToChapter) {
      chapterInfo.chapterContent = generatePaidPageContent();
    }

    return chapterInfo;
  }

});
</script>
<style lang="scss" scoped>
@import "./sub/readerTheme.scss";

.__reader__ {
  width: 100vw;
  height: 100vh;
  background-color: var(--pageBackgroundColor);

  .reader-main {
    overflow: hidden;
    width: 100%;
    height: calc(100% - 48px);
  }
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;

  .content-pages {
    width: 100%;
    height: 100%;
    min-height: 800px;
    overflow-y: auto;
  }
}

.simplified-reader-wrapper {
  .Bookmark {
    display: none;
  }
}

// 自定义滚动条样式
.content-pages {
  &::-webkit-scrollbar {
    width: 10px !important;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 5px;

    &:hover {
      background: #a0a0a0;
    }
  }
}
</style>
