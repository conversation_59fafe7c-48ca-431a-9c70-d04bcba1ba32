<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-16 10:13:16
 * @LastEditTime: 2025-02-27 14:44:13
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Content.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  display: flex;
  min-width: 1266px;
  justify-content: space-between;
  box-sizing: border-box;
  // color: var(--fontColor);

  .content-pages {
    width: 100%;
    height: 100%;
    min-height: 800px;
    overflow-y: auto;
  }
}

.SelectionBox {
  position: relative;
  z-index: 999;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
}
</style>

<template>
  <div class="content">
    <Menus ref="menusRef" :init-catalog-id="initCataId" />
    <main
      class="content-pages __hidden-scroll__"
      ref="pageContentContainer"
      :id="PAGE_ITEMS_CONTAINER_ID"
      @click.stop="mouseup"
      @mousedown.stop="mousedown"
    >
      <!-- <template v-if="store.flex === 'two'">
        <Pages_x :pageData="data" v-if="store.pageFlippingMethod === 'x'" />
        <Pages_r :pageData="data" v-if="store.pageFlippingMethod === 'r'" />
      </template> -->
      <template v-if="store.flex === 'one'">
        <!-- <Pages_x_one :pageData="data" v-if="store.pageFlippingMethod === 'x'" /> -->
        <reflowable-layout v-if="store.pageFlippingMethod === 'y'" />
        <folio-simulation v-if="store.pageFlippingMethod === 'r'" />
      </template>
    </main>
    <Tool @edit="editNote" ref="toolRef" />
  </div>
  <div
    class="SelectionBox"
    v-if="selectionTableBar.isShow"
  >
    <Selection
      :x="selectionTableBar.x"
      :y="selectionTableBar.y"
      :elementText="selectionTableBar.selectedElementText"
      :elementList="selectionTableBar.elementList"
      :elementIdList="selectionTableBar.elementIdList"
      :close="closeSelectionBox"
      :isStatus="selectionTableBar.isShow"
    />
  </div>
  <AddNote
    v-if="noteIsShow"
    :isShow="noteIsShow"
    :setIsShow="setIsShow"
    :elementIdList="selectionTableBar.elementIdList"
    :elementText="selectionTableBar.selectedElementText"
    :editNoteData="editNoteData"
    @cancel="noteIsShow = false"
    @confirm="getBookNoteList"
    :chapter-id="onSelectedPageNodeData.chapterId"
    :page-index-in-chapter="onSelectedPageNodeData.pageIndexInChapter"
    :book-id="store.comprehensiveBookData.bookId"
  />
  <PreviewDialog />
  <Jiucuo :uuid="uid" :elementList="selectionTableBar.elementList" />
  <FloatNote />
  <tts-panel></tts-panel>
  <!-- <ReadingPanel /> -->
  <imagePreview> </imagePreview>
</template>

<script setup>
import Menus from "./Menus.vue";
import imagePreview from "@/components/galleryImagePreview/index.vue";
import ttsPanel from "@/views/reader/sub/Tool/TTSPanel";
// import Pages_x_one from './Pages/Pages_x_one.vue'
// import Pages_x from './Pages/Pages_x.vue'
// import Pages_r from './Pages/Pages_r.vue'
import ReflowableLayout from "./Pages/ReflowableLayout.vue";
import FolioSimulation from "./Pages/FolioSimulation.vue";
import Tool from "./Tool.vue";
import Jiucuo from "./Tool/Jiucuo.vue";
import AddNote from "./Tool/AddNote.vue";
import FloatNote from "./Tool/FloatNote.vue";
import Selection from "./Tool/Selection.vue";
import ReadAloud from "./Tool/ReadAloud.js";
// import ReadingPanel from "./Tool/ReadingPanel.vue";
import PreviewDialog from "./Tool/PreviewDialog.vue";
import {
  onMounted,
  ref,
  reactive,
  watch,
  onBeforeUnmount,
  getCurrentInstance,
  toRaw,
  nextTick,
} from "vue";
import useReader, { READING_MODE } from "@/store/modules/reader";
import useAutoReading from "@/store/modules/autoReading";
import useTTSReading from "@/store/modules/readAloud";
import { copyText, uuid } from "@/utils/index.js";
import { bookCopyText, saveBookConfig } from "@/api/book/reader";
import {
  PAGE_ITEMS_CONTAINER_ID,
  PAGE_ITEMS_CSS_CLASS,
  BOOK_PAGE_INDEX,
  CHAPTER_PAGE_NUMBER,
  PAGE_CHAPTER_ID_ATTR,
  findClosestPageNode,
  generatePaidPageContent,
  hasAccessToReadChapter,
  whenLoaded,
  PAGE_TURNING_ERROR_MAPPER,
  unregisterKeyboardEvent,
  registerKeyboardEvent,
  unregister,
  TEXT_SELECTION_TOOLBAR_ID
} from "@/utils/reader";
import {
  getChapters,
  getBookMark,
  getBookLine,
  getBookNote,
} from "@/api/book/reader";
import { throttle, debounce } from "lodash";
import { ElMessage, ElMessageBox } from "element-plus";
import { onBeforeRouteLeave } from "vue-router";
import dayjs from "dayjs";
const props = defineProps({
  initChapterId: {
    type: String,
    default: "",
  },
  initCataId: {
    type: String,
    default: "",
  },
});
const store = useReader();
const autoReadingStore = useAutoReading();
const ttsReadingStore = useTTSReading();
const { proxy } = getCurrentInstance();
const toolRef = ref(null);
const menusRef = ref(null);
const uid = ref("a");
let noteIsShow = ref(false);
const editNoteData = ref({});
let pageScrollDirection = "";
const setIsShow = (v) => (noteIsShow.value = v);
// 监听样式设置变化,解决行高不变的问题
watch(
  () => store.styleSetting["line-height"],
  (newVal, oldVal) => {
    // 遍历所有p标签
    var element = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
    var pTags = element.querySelectorAll("p");
    pTags.forEach(function (pTag) {
      pTag.style.lineHeight = store.styleSetting["line-height"];
    });
  },
  { deep: true }
);
/**在Note组件调用AddNote组件*/
const editNote = (type, val) => {
  if (type === "note") {
    editNoteData.value = val;
    noteIsShow.value = true;
  }
};
// const data = ref([]);
let selectionList = [];
const selectionTableBar = reactive({
  selectedElementText: '',
  isShow: false,
  x: 0,
  y: 0,
  elementList: [],
  elementIdList: []
});
const start = reactive({ x: 0, y: 0 });
const pageContentContainer = ref("pageContentContainer");
let onSelectedPageNodeData = ref({
  pageIndexInChapter: undefined,
  pageIndexInBook: undefined,
  chapterId: undefined,
});
const getSelectedText = () => {
  return selectionList.map((ele) => ele.innerText).join('')
};

const closeSelectionBox = (type) => {
  const selectedText = getSelectedText();
  const textNode = selectionTableBar.elementList[0];
  const selectedNodeId = textNode.getAttribute("id");
  const selectedNode = document.querySelector(
    `#${PAGE_ITEMS_CONTAINER_ID} [id="${selectedNodeId}"]`
  );
  const closestPageNode = findClosestPageNode(selectedNode);
  onSelectedPageNodeData = {
    chapterId: closestPageNode?.getAttribute(PAGE_CHAPTER_ID_ATTR),
    pageIndexInChapter: closestPageNode?.getAttribute(CHAPTER_PAGE_NUMBER),
    pageIndexInBook: closestPageNode?.getAttribute(BOOK_PAGE_INDEX),
  };
  switch (type) {
    case "note":
      editNoteData.value = {};
      noteIsShow.value = true;
      break;

    case "fuzhi":
      bookCopyText({
        bookId: store.comprehensiveBookData.bookId,
        copyText: selectedText,
      }).then((res) => {
        if (res.code === 200) {
          copyText(selectedText).then(ElMessage.success("复制成功"));
        }
        //限制复制次数
        if (res.code == 403) {
          ElMessage.error(res.msg);
        }
      });
      break;

    case "baike":
      window.open(`https://baike.baidu.com/search/none?word=${selectedText}`);
      break;

    case "translate":
      store.aiDialog("AI翻译", selectedText);
      break;

    case "spirit":
      store.aiDialog("阅读精灵", selectedText);
      break;

    case "jiucuo":
      uid.value = uuid();
      break;

    case "shuben":
      window.open(
        `https://hanyu.baidu.com/hanyu-page/term/detail?wd=${selectedText}`
      );
      break;

    case "langdu":
      ttsReadingStore.startReadWithAloud(
        selectedText,
        selectionTableBar.elementList
      );
      break;

    default:
      break;
  }
  selectionTableBar.isShow = false;
  selectionList = [];
};

const mouseup = (e) => {
  function getSelectedNodes() {
    const selectedNodes = [];
    // 获取当前选中对象
    const selection = window.getSelection();
    if (!selection.isCollapsed && selection.rangeCount > 0) {
      // 通过 range 对象获取选中文本的开始和结束节点
      const range = selection.getRangeAt(0);
      // 创建一个 DocumentFragment，为循环遍历范围中的节点准备
      const fragment = range.cloneContents();

      // 获取文本节点
      const startTextNode = range.startContainer;
      const endTextNode = range.endContainer;

      // 如果仅仅选中了一个字
      if (startTextNode === endTextNode) {
        // 获取文本节点的上一级节点
        const parentNode = startTextNode.parentNode;
        selectedNodes.push(parentNode.cloneNode(false));
        return selectedNodes;
      }

      // 递归遍历并收集所有节点
      function collectNodes(node) {
        if (node.classList?.contains('Page-item-header') || node.classList?.contains('Page-item-footer')) {
          crossPageSelection = true;
        }
        if (crossPageSelection) {
          return
        }
        if (node?.classList?.contains?.('reader-text') && node?.getAttribute?.('sign') === 'selection') {
          selectedNodes.push(node);
        }
        node.childNodes.forEach(collectNodes);
      }
      fragment.childNodes.forEach(collectNodes);
    }
    if (crossPageSelection) {
      return []
    }
    // selection.isCollapsed情况下，selectedNodes的长度是0
    return selectedNodes;
  }

  let crossPageSelection = false;

  if (crossPageSelection) {
    // 如果进行了跨page选取文字, 则不进行任何处理
    selectionTableBar.isShow = false;
    return;
  }

  // 这里添加timeout是因为click事件会在mouseup事件之前触发，所以需要等待click事件处理完后再进行处理
  setTimeout(() => {
    selectionList = getSelectedNodes();
    if (selectionList.length > 300) {
      ElMessage.error("笔记划线超过300字，请重新选择");
      selectionList = [];
      selectionTableBar.elementList = [];
      selectionTableBar.elementIdList = [];
      return;
    }
    
    selectionTableBar.x = Math.abs(start.x - e.clientX) / 2 + (e.clientX <= start.x ? e.clientX : start.x);
    selectionTableBar.y = (e.clientY <= start.y ? e.clientY : start.y) - 10;
    selectionTableBar.elementList = selectionList;
    selectionTableBar.elementIdList = selectionList.map(item => item.getAttribute('id'));
    selectionTableBar.selectedElementText = getSelectedText();
    if (selectionList.length <= 300 && selectionList.length > 0) {
      selectionTableBar.isShow = true;
    }
  }, 0);
};

const mousedown = (e) => {
  const selectionToolbar = e.target.closest('#' + TEXT_SELECTION_TOOLBAR_ID);
  if (!selectionToolbar) {
    // 如果没有点击文字自定义工具条，则隐藏工具条
    selectionTableBar.isShow = false;
  }
  start.x = e.clientX;
  start.y = e.clientY;
};
function calculatePreviousPageHeight(allPageNodes, currentIndexInTheNodeArray) {
  let heightInTotal = 0;
  for (let i = currentIndexInTheNodeArray; i > -1; i--) {
    heightInTotal += allPageNodes[i].offsetHeight;
  }
  return heightInTotal;
}
function hit2Top(scrollTopValue) {
  if (scrollTopValue === 0) {
    return true;
  }
  return false;
}
function hit2Bottom(
  pageIndexInContainer,
  pageTotalInView,
  containerHeight,
  remainingVisibleHeightOfCurrentPageInContainer
) {
  // console.log('判断是否到底页面底部：', pageIndexInContainer === (pageTotalInView - 1), Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight))
  if (
    pageIndexInContainer === pageTotalInView - 1 &&
    Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight) <
      80
  ) {
    return true;
  }
  return false;
}
let loadNewChapterTimer = null;
let cumulatedHitTimesOnWill = 0;
let need2Jump2NextChapter = false;
let need2Jump2PreviousChapter = false;
let loadingChapterInProgress = false;
/**
 * @description 该方法只处理 真实翻页的情况下，进入下一页
 * @param nextChapterOrPreviousChapter
 */
function autoReadingLoadChapter(nextChapterOrPreviousChapter) {
  clearTimeout(loadNewChapterTimer);
  if (store.reading === "automaticReading") {
    loadNewChapterTimer = setTimeout(() => {
      if (nextChapterOrPreviousChapter === "next") {
        if (!store.isLastChapter()) {
          ElMessage.info("即将进入下一章");
        }
        store.nextPage().catch((error) => {
          if (error.code === PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.code) {
            autoReadingStore.pause();
            ElMessage.info("您已阅读到教材最后一页，5秒后将退出自动阅读");
            setTimeout(() => {
              store.setReading(READING_MODE.GENERAL);
            }, 5000);
          }
          return error;
        });
      } else if (nextChapterOrPreviousChapter === "previous") {
        // console.log('去往上一页')
        store.lastPage();
      }
    }, 2000);
  }
}

const promptUserWithChapterLoading = throttle(
  () => {
    if (loadingChapterInProgress) {
      ElMessage.info("正在加载章节数据");
    }
  },
  4000,
  {
    trailing: true,
  }
);

const scrollCallback = throttle(
  () => {
    // 暂时约定滚轮上下滑动的时候，隐藏文字自定义工具条
    selectionTableBar.isShow = false;

    // console.log('scrollCallback')
    // 真实阅读模式，不需要计算当前滑动到第几页, 也不会触发这里scroll事件
    const pageContainer = document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`);
    const containerHeight = pageContainer.offsetHeight;
    const pagesDom = document.querySelectorAll(
      `#${PAGE_ITEMS_CONTAINER_ID} .${PAGE_ITEMS_CSS_CLASS}`
    );
    let scrollTopValue = pageContainer.scrollTop;
    let targetPage = null;

    // 自动阅读模式只能向下浏览，不会向上浏览
    if (hit2Top(scrollTopValue) && store.reading !== "automaticReading") {
      need2Jump2PreviousChapter = true;
      need2Jump2NextChapter = !need2Jump2PreviousChapter;
      autoReadingLoadChapter("previous");
      // console.log('hit2top { need2Jump2PreviousChapter }:', need2Jump2PreviousChapter)
    }
    // 当页面滑动到最底部或最顶部的时候，该事件不会被继续触发, 所以仅在scroll事件触发的时候会将cumulatedHitTimesOnWill次数设置为0
    cumulatedHitTimesOnWill = 0;
    for (
      let i = 0,
        len = pagesDom.length,
        remainedVisibleHeightOfCurrentPageInContainer;
      i < len;
      i++
    ) {
      // 当前页内容仍然占据整个容器高度
      // console.log('data value:', scrollTopValue, pagesDom[i].offsetHeight, containerHeight)
      const cumulatedPreviousPageHeight = calculatePreviousPageHeight(
        pagesDom,
        i - 1
      );
      const remainingScrollValue =
        scrollTopValue - cumulatedPreviousPageHeight - pagesDom[i].offsetHeight;
      if (remainingScrollValue > 0) {
        // 当前node完全消失在容器内, 则继续计算
        // scrollTopValue = remainingScrollValue
        continue;
      }
      remainedVisibleHeightOfCurrentPageInContainer =
        cumulatedPreviousPageHeight + pagesDom[i].offsetHeight - scrollTopValue;
      if (remainedVisibleHeightOfCurrentPageInContainer >= containerHeight) {
        // 当前页面占据整个容器
        targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
        // break
      } else if (
        remainedVisibleHeightOfCurrentPageInContainer < containerHeight &&
        remainedVisibleHeightOfCurrentPageInContainer > containerHeight / 2
      ) {
        // 当前页面占据整个容器的二分之一以上
        targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
        // break
      } else if (
        remainedVisibleHeightOfCurrentPageInContainer <
        containerHeight / 2
      ) {
        // 当前页面占据整个容器不到二分之一
        if (i < len) {
          targetPage = pagesDom[i + 1].getAttribute(BOOK_PAGE_INDEX);
          // break
        } else {
          // 按理说不应该进入这个分支, 需要检查页面显示效果是否正确
          // debugger
        }
        break;
      } else {
        // 按理说不应该进入这个分支,当前页面不可能占据页面不到二分之一，又大于整个容器
        // debugger
        // if (i === (len - 1) && remainedVisibleHeightOfCurrentPageInContainer <= 100) {
        //   targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX)
        //   // 加载下一章
        // }
      }
      // pageDom的长度和store.pageData的长度一致
      if (
        hit2Bottom(
          i,
          len,
          containerHeight,
          remainedVisibleHeightOfCurrentPageInContainer
        )
      ) {
        need2Jump2NextChapter = true;
        need2Jump2PreviousChapter = !need2Jump2NextChapter;
        autoReadingLoadChapter("next");
        // console.log('hit2bottom { need2Jump2NextChapter}:', need2Jump2NextChapter)
      }
      if (targetPage != null) {
        break;
      }
    }
    if (targetPage != null) {
      // 阅读器在自动阅读状态下切换翻页方式，由于使用了节流throttle，所以这个scrollCallback会有延迟, 会导致targetPage是null，最优化的方式是修改当前页码的判断
      store.setCurrentPageIndex(targetPage);
    }
  },
  400,
  {
    trailing: true,
  }
);

const wheelCallback = throttle(
  (event) => {
    if (loadingChapterInProgress) {
      promptUserWithChapterLoading();
      return;
    }
    // 自动阅读模式下，鼠标滚轮事件不会被触发
    // console.log('wheelcallback主动触发')
    if (event.deltaY < 0) {
      pageScrollDirection = "up";
    } else {
      pageScrollDirection = "down";
    }
    if (need2Jump2PreviousChapter && pageScrollDirection === "up") {
      cumulatedHitTimesOnWill++;
      // console.log('下滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
      if (cumulatedHitTimesOnWill > 2) {
        cumulatedHitTimesOnWill = 0;
        loadingChapterInProgress = true;
        return store
          .lastPage()
          .catch((error) => {
            if (
              error.type === PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.code
            ) {
              ElMessage.warning(
                PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.msg
              );
            }
            return error;
          })
          .finally(() => {
            loadingChapterInProgress = false;
          });
      }
    } else if (need2Jump2NextChapter && pageScrollDirection === "down") {
      cumulatedHitTimesOnWill++;
      // console.log('上滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
      if (cumulatedHitTimesOnWill > 2) {
        cumulatedHitTimesOnWill = 0;
        loadingChapterInProgress = true;
        return store.nextPage().finally(() => {
          loadingChapterInProgress = false;
        });
      }
    }
  },
  200,
  {
    trailing: true,
  }
);

// hook
onBeforeRouteLeave(() => {
  // 离开阅读器会重置阅读信息，包括书籍目录信息、章节内容
  store.deauthorizeToTheBook();
  unregister();
});
onMounted(() => {
  registerKeyboardEvent("reader", (e) => {
    //左右箭头翻页
    if (e.keyCode === 39) {
      store.nextPage();
    } else if (e.keyCode === 37) {
      store.lastPage();
    }
  });
  nextTick(() => {
    pageContentContainer.value.addEventListener("scroll", scrollCallback);
    pageContentContainer.value.addEventListener("wheel", wheelCallback);
  });
});
onBeforeUnmount(() => {
  ElMessageBox.close();
  unregisterKeyboardEvent("reader");
  store.deauthorizeToTheBook();
  pageContentContainer.value.removeEventListener("scroll", scrollCallback);
  pageContentContainer.value.removeEventListener("wheel", wheelCallback);
});

let bookReadingSettings = {};
const saveReadingSetting = debounce(() => {
  saveBookConfig(bookReadingSettings).finally(() => (bookReadingSettings = {}));
}, 500);
watch(
  () => [store.comprehensiveBookData.currentPageIndex, store.chapterId],
  (
    [nValueCurrentPageIndex, nValueChapterId],
    [oValueCurrentPageIndex, oValueChapterId]
  ) => {
    bookReadingSettings.configId = store.configId;
    bookReadingSettings.lastSeeDate = dayjs().format("YYYY-MM-DD");
    if (
      nValueCurrentPageIndex &&
      nValueCurrentPageIndex !== oValueCurrentPageIndex
    ) {
      bookReadingSettings.lastPageNumber =
        store.getPageIndexInChapterByPageIndexInBook(nValueCurrentPageIndex);
    }
    if (nValueChapterId && nValueChapterId !== oValueChapterId) {
      bookReadingSettings.lastChapterId = nValueChapterId;
      scrollCallback();
    }
    saveReadingSetting();
  }
);

watch(
  // 每个教材的基本信息只加载一次，如果不切换教材，该信息不会发生变化
  () => [store.comprehensiveBookData.bookId],
  ([newValue4BookId]) => {
    if (!newValue4BookId) {
      return;
    }
    // 加载教材对应的目录信息
    getChapterList(newValue4BookId).then(() => {
      // 加载最后一次阅读章节的信息
      // 有权限访问该教材, 则加载教材的章节目录信息，以及最近一次阅读的章节以及第几页
      getContentData().then(() => {
        whenLoaded(preloadChaptersContentData);
      });
    });
  }
);

watch(
  () => store.pageFlippingMethod,
  (nValue) => {
    if (nValue === "r") {
      // 真实分页后不通过鼠标滚轮事件更新当前第几页
      pageContentContainer.value.removeEventListener("scroll", scrollCallback);
      pageContentContainer.value.removeEventListener("wheel", wheelCallback);
    } else {
      nextTick(() => {
        pageContentContainer.value.addEventListener("scroll", scrollCallback);
        pageContentContainer.value.addEventListener("wheel", wheelCallback);
      });
    }
  }
);
function preloadChaptersContentData() {
  // store.preloadChaptersContentData()
}
function handleLastReadingData() {
  if (store.comprehensiveBookData.lastTimeReadingFlippingMethod) {
    store.setPageFlippingMethod(
      store.comprehensiveBookData.lastTimeReadingFlippingMethod
    );
  }

  if (store.comprehensiveBookData.lastReadingMode) {
    store.setReading(store.comprehensiveBookData.lastReadingMode);
  }
}
async function getContentData() {
  handleLastReadingData();

  getBookMark(store.comprehensiveBookData.bookId).then((res) => {
    if (res.code === 200) store.bookMarkData = res.data;
  });

  getBookLine({ bookId: store.comprehensiveBookData.bookId, color: null }).then(
    (res) => {
      if (res.code === 200) store.setBookLineData(res.data);
    }
  );
  // 默认需要拉取笔记数据，以便标注教材中笔记位置
  getBookNote({
    bookId: store.comprehensiveBookData.bookId,
    sort: 1,
    noteType: 1,
  }).then((res) => {
    if (res.code === 200) store.setNoteList(res.data);
  });

  let lastReadingChapterId = "";
  let lastReadingPageIndexInChapter = 1;
  if (
    store.comprehensiveBookData.lastReadingPageIndexInChapter &&
    store.comprehensiveBookData.lastReadingChapterId
  ) {
    lastReadingChapterId = store.comprehensiveBookData.lastReadingChapterId;
    lastReadingPageIndexInChapter =
      store.comprehensiveBookData.lastReadingPageIndexInChapter;
  } else if (!store.comprehensiveBookData.lastReadingChapterId) {
    lastReadingChapterId =
      store.comprehensiveChapterAndCatalogData.chaptersData[0]?.chapterId;
  }
  // 从教材详情页面跳转过来会带chapterId和catalogId
  if (props.initChapterId) {
    lastReadingPageIndexInChapter = 1;
    lastReadingChapterId = props.initChapterId;
    store.comprehensiveBookData.lastReadingChapterId = lastReadingChapterId;
  }
  if (props.initCataId && props.initChapterId) {
    const catalogData = await store.findCatalogDataById(
      props.initCataId,
      props.initChapterId
    );
    lastReadingPageIndexInChapter = catalogData.pageNumber || 1;
  }
  // 对上一次阅读的章节页码进行修正。
  const validLastReadingPageIndexInChapter =
    await store.repairePageIndexInChapter(
      lastReadingChapterId,
      lastReadingPageIndexInChapter
    );
  // 进入上次阅读的章节以及页面
  return store.jumpToChapter(
    lastReadingChapterId,
    validLastReadingPageIndexInChapter
  );
  // .catch((resp) => {
  //   if (resp.code === PAGE_TURNING_ERROR_MAPPER.NEED_TO_PAY_TO_READ.code) {
  //     store.setData(generatePaidPageContent(), lastReadingChapterId, false)
  //   }
  //   return resp
  // })
}
function getBookNoteList() {
  noteIsShow.value = false;
  getBookNote({
    bookId: store.comprehensiveBookData.bookId,
    sort: 1,
    noteType: 1,
  }).then((res) => {
    if (res.code === 200) store.setNoteList(res.data);
  });
}
// 加载目录信息
function getChapterList(bookId) {
  let chapterInfoArray = [];
  return getChapters(bookId).then((res) => {
    if (res.code === 200) {
      chapterInfoArray = res.data || [];
      console.log(res.data);
      debugger
      let pagesCnt = 0;
      chapterInfoArray.forEach((chapterInfo) => {
        chapterInfo.catalogId = chapterInfo.chapterId;
        chapterInfo.title = chapterInfo.chapterName || "";
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages);
        chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo);
        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1;
        chapterInfo.children = chapterInfo.hasAccessToChapter
          ? chapterInfo.catalogs || []
          : [];

        if (!chapterInfo.hasAccessToChapter) {
          chapterInfo.chapterContent = generatePaidPageContent();
        }
        pagesCnt += chapterTotalPages;
        delete chapterInfo.catalogs;
      });
      // 设置所有章节的信息
      store.setChaptersData(chapterInfoArray, pagesCnt);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  });
}

defineExpose({
  menusRef,
  toolRef,
});
</script>
