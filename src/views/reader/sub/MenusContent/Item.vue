<style lang="scss" scoped>
.menuitem-title {
  display: inline-block;
  width: 100%;
 
}
.menuitem-bold {
  font-weight: bold;
}
</style>

<template>
  <template v-for="(ele, i) in option" :key="i">
    <el-sub-menu
      :index="ele.catalogId"
      v-if="ele.children?.length"
      :title="returnText(ele.title)"
    >
      <template #title>
        <span
          @click.stop="headingItemClickHandler(ele)"
          class="__ellipsis__ menuitem-title menuitem-bold"
        >{{ returnText(ele.title) }}</span>
      </template>
      <span><RecursionItem :option="ele.children" /></span>
    </el-sub-menu>
    <el-menu-item
      :index="ele.catalogId"
      v-else
      :title="returnText(ele.title)"
      class="__menuItemRefs__"
    >
      <template #title>
        <span
          @click="headingItemClickHandler(ele)"
          :class="['__ellipsis__', 'menuitem-title', ele.children?.length ? 'menuitem-bold' : '']"
        >{{ returnText(ele.title) }}</span><el-icon v-if="!ele.hasAccessToChapter" class="icon-lock"><Lock /></el-icon>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup>
import RecursionItem from "./RecursionItem.vue";
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";
import { highlightKeyWordSmooth, needToPayToRead } from "@/utils/reader";

const bookReadingStore = useReader();
const props = defineProps({
  option: {
    type: Array,
    required: true,
  },
});

// 选择目录时 前面有一个图片  图片不能展示在本前面 输出了null
const returnText = (value) => {
  // 使用正则表达式删除前面的 "null"（包括多个连续的 "null"）
  value = value.replace(/^null+/, "");
  // 使用正则表达式删除后面的 "null"（包括多个连续的 "null"）
  value = value.replace(/null+$/, "");
  return value;
};

// 方法
function headingItemClickHandler(headingItem) {
  headingItem = toRaw(headingItem);
  debugger
  if (!headingItem.hasAccessToChapter) {
    return needToPayToRead(
      bookReadingStore.comprehensiveBookData.bookId,
      headingItem.chapterName
    ).then(() => {
      const primaryBook = bookReadingStore.findPrimaryBook();
      if (primaryBook) {
        router.push({
          path: "/book-detail",
          query: { key: primaryBook.bookId },
        });
      }
    });
  }
  if (
    headingItem.level === 0 ||
    (!headingItem.domId && !headingItem.pageNumber)
  ) {
    bookReadingStore.jumpToChapter(headingItem.chapterId);
    // } else if (headingItem.pageNumber) {
    //   bookReadingStore.jumpToChapter(headingItem.chapterId, headingItem.pageNumber)
  } else if (headingItem.domId) {
    bookReadingStore
      .jumpToPageBasedOnNodeId(headingItem.domId, headingItem.chapterId)
      .then(() => {
        // let targetHeadingDom = document.querySelector(
        //   `h${headingItem.level}[data-toc-id="${headingItem.domId}"]`
        // );

        // 原h${headingItem.level}去掉 事实上headingItem.level只是目录层级 不是h标签的等级 如果h1下直接添加的是H4会找不到 因为headingItem.level此时等于2
        console.log(headingItem.domId);
        let targetHeadingDom = document.querySelector(
            `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${headingItem.domId}"]`
        );

        // 节头跳转问题修改
        if (!targetHeadingDom) {
          targetHeadingDom = document.querySelector(
              `div[id="${headingItem.domId}"]`
          );
        }

        if (targetHeadingDom) {
          highlightKeyWordSmooth(targetHeadingDom);
        }
      });
  }
}
// hook
</script>
