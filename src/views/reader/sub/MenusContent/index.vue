<!-- 模块说明 -->
<style lang="scss" scoped>
$bordercolor: #e3e3e3;
.MenusContent {
  height: 100%;
  .MenusContent-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $bordercolor;
    padding-bottom: 16px;
    box-sizing: border-box;
    & > span:nth-of-type(1) {
      font-weight: 600;
      font-size: 16px;
      color: var(--fontColor);
    }
    & > span:nth-of-type(2) {
      font-weight: 400;
      font-size: 12px;
      color: #0966b4;
      line-height: 12px;
      cursor: pointer;
    }
  }

  .MenusContent-main {
    width: 100%;
    height: calc(100% - 37px);
    overflow-y: auto;
    .MenusContent-main-item {
      width: 100%;
      font-size: 14px;
      height: 44px;
      line-height: 44px;
      text-align: left;
      cursor: pointer;
      padding-left: 20px;
      box-sizing: border-box;
      border-bottom: 1px solid $bordercolor;
      &:hover {
        background-color: var(--hoverBackgroundColor);
        color: var(--hoverfont);
      }
    }
  }
}

.elMenuStyle {
  border: 0;
  ::v-deep(.el-menu-item.is-active) {
    color: #0966b4;
  }
}

</style>
<style lang="scss"></style>
<template>
  <div class="MenusContent">
    <div class="MenusContent-header">
      <span>目录</span>
      <span @click="openMenu" ref="menuStatusRef"
        >{{ menuStatus ? `折叠` : `展开` }}全部目录</span
      >
    </div>

    <!-- __hidden-scroll__    滚动条 -->
    <div class="MenusContent-main">
      <el-menu class="elMenuStyle" ref="menuItemsRef">
        <Item :option="chapterInfoArray" />
      </el-menu>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, watch, defineProps } from "vue";
import Item from "./Item.vue";
import useReader from "@/store/modules/reader";

const readerStore = useReader();
const chapterInfoArray = ref([]);
const menuStatus = ref(false);
const menuStatusRef = ref(null);
const menuItemsRef = ref(null);
let toExpandeCatalogIds = [];

const props = defineProps({
  initCatalogId: {
    type: String,
    default: "",
  },
});
const openMenu = () => {
  if (!menuStatus.value) {
    toExpandeCatalogIds.forEach((catalogId) => {
      menuItemsRef.value.open(catalogId);
    });
  } else {
    toExpandeCatalogIds.forEach((catalogId) => {
      menuItemsRef.value.close(catalogId);
    });
  }
  menuStatus.value = !menuStatus.value;
};

function deepInit(data, hasAccessToChapter = true, depth = 0) {
  data.forEach((ele) => {
    ele.level = depth;
    ele.hasAccessToChapter = ele.hasAccessToChapter ?? hasAccessToChapter;
    if (ele.children?.length > 0) {
      toExpandeCatalogIds.push(ele.catalogId);
    }
    if (ele.children?.length)
      deepInit(ele.children, ele.hasAccessToChapter, depth + 1);
  });
}

function findParentCatalog(data, targetCatalogId, callbackFn) {
  for (let i = 0; i < data.length; i++) {
    const ele = data[i];
    if (ele.catalogId === targetCatalogId) {
      callbackFn(ele);
      return;
    }
    if (ele.children?.length) {
      findParentCatalog(ele.children, targetCatalogId, callbackFn);
    }
  }
}

watch(
  () => [
    readerStore.comprehensiveChapterAndCatalogData.chaptersData,
    readerStore.comprehensiveBookData.lastReadingChapterId,
  ],
  (nValues) => {
    const [nValueChapterData, nValueChapterId] = nValues ?? [];
    // console.log(nValueChapterData)
    const tmpChapterData = JSON.parse(JSON.stringify(nValueChapterData));
    console.log(tmpChapterData);
    debugger
    toExpandeCatalogIds = [];
    deepInit(tmpChapterData);
    let defaultOpendMenu = "";
    chapterInfoArray.value = tmpChapterData;
    if (tmpChapterData[0]?.chapterId) {
      defaultOpendMenu = tmpChapterData[0]?.chapterId;
    }
    if (
      nValueChapterId &&
      nValueChapterData.find(
        (chapterData) => chapterData.chapterId === nValueChapterId
      )
    ) {
      defaultOpendMenu = nValueChapterId;
    }

    if (props.initCatalogId && props.initCatalogId != nValueChapterId) {
      const grandParentCatalogData = tmpChapterData.filter(
        (chapterDataItem) => chapterDataItem.catalogId === nValueChapterId
      );
      findParentCatalog(
        grandParentCatalogData,
        props.initCatalogId,
        (parentCatalog) => {
          defaultOpendMenu = parentCatalog.parentId;
        }
      );
    }

    if (defaultOpendMenu) {
      nextTick(() => {
        menuItemsRef.value.close(defaultOpendMenu);
      });
    }
  },
  { immediate: true }
);
defineExpose({
  menuStatusRef,
});
</script>
