<template>
  <div
    class="headerbg"
    :style="style"
  >
    <div class="header-title" :style="`${store.templateStyle?.theme=='light'?'color:#fff !important;':'color:#333 !important;'}`">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const props = defineProps({
  chapterHeaderUrl: {
    type: String,
    default: null,
  },
  chapterHeaderHeight: {
    type: Number,
    default: null,
  },
});

const style = computed(() => {
  let template = store.templateStyle;
  const style = {
    background: `url(${template?.chapterHeaderUrl}) no-repeat`,
    backgroundSize: 'cover',
    height: `${template?.chapterHeaderHeight / 2.5}px`,
  }
  if (props.chapterHeaderUrl != null) {
    style.background = `url(${props.chapterHeaderUrl}) no-repeat`
  }
  if (props.chapterHeaderHeight != null) {
    style.height = `${props.chapterHeaderHeight / 2.5}px`
  }
 
  return style
})

</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  margin: 20px 0;
  display: flex;
  align-items: center;
  .header-title {
   
    outline: none;
    width: 100%;
  }
}
</style>
