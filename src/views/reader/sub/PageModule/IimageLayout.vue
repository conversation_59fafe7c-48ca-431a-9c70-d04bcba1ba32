<!-- 模块说明 -->
<style lang="scss" scoped>
.imageLayout {
  margin-top: 16px;
  text-align: center;
  .imageTitle {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>

<template>
  <div
    :style="{
      display: 'flex',
      justifyContent: nodeAlign,
      maxWidth: '100%',
     
    }"
  >
    <div class="imageLayout" :id="id">
      <el-image
        :src="src"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[src]"
        @click="handleClick"
        style="cursor: pointer"
        hide-on-click-modal
        :initial-index="4"
        @show="handleShow"
        :style="{
          width: width>696?'100%':width + 'px',
          height: height + 'px',
          transform:
            flipX || flipY
              ? `rotateX(${flipX ? '180' : '0'}deg) rotateY(${flipY ? '180' : '0'}deg)`
              : 'none',
        }"
      />
      <div class="imageTitle">
        <a
          v-if="linkAddress"
          :href="linkAddress"
          target="_blank"
          
        >
          <p v-if="isShowNo == 1" style="line-height: 1.5; width: 60px;flex-shrink: 0;text-align: center;">
            {{ number }}
          </p>
          <template v-if="isShowImageTitle == '1'"><slot /></template>
        </a>
        <div
          style="display: flex; justify-content: center; align-items: center;text-align:left;"
          v-else
        >
          <p v-if="isShowNo == 1" style="line-height: 1.5; width: 60px;flex-shrink: 0;text-align: center;">
            {{ number }}
          </p>
          <template v-if="isShowImageTitle == '1'"><slot /></template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
import { getBookResource, getChapters } from "@/api/book/reader";
import usePreview from "@/store/modules/preview.js";
import useReader from "@/store/modules/reader.js";
const previewStore = usePreview();
const store = useReader();

const props = defineProps({
  height: [Number, String],
  src: String,
  nodeAlign: String,
  name: String,
  isShowImageTitle: String,
  number: String,
  id: String,
  width: [Number, String],
  linkAddress: String,
  isShowNo: String,
  flipX: Boolean,
  flipY: Boolean,
});
async function handleClick() {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 0,
    chapterId: undefined,
    fileType: "1",
  };
  const res = await getBookResource(params);
  if (res.code === 200) {
    const result = res.data;
    let imageResources = [];
    result.forEach((item) => {
      imageResources = imageResources.concat(item.bookResourcesList);
    });
    previewStore.setPreviewImages(imageResources);
    nextTick(() => {
      // previewStore.setCurrentImage(props.src)
    });
  }
}


const handleShow= (e)=>{
  const elements = document.querySelectorAll('.backgroundImg_bg');
  
  elements.forEach((item) => {
    item.addEventListener('click', function() {
      // 遍历所有元素，修改非点击元素的 z-index
      elements.forEach(el => {
        if (el !== this) {
          el.style.zIndex = '0'; // 其他元素置底
        } else {
          this.style.zIndex = '1'; // 被点击的元素置顶
        }
      });
    });
  })
  // console.log(e)
  // const divStyle=document.getSelection
  // console.log(divStyle)
}
</script>
