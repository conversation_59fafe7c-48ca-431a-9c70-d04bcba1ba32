<template>
  <div class="page-container" :style="dialogStyle">
    <div class="content-mobile">
      <!-- 开始页面 -->
      <div class="start-screen" v-if="!showResult && startFlag">
        <div class="scale-name-container">
          <div class="scale-name-mobile">{{ scaleForm.scaleName }}</div>
          <div class="overlap-box-mobile"></div>
        </div>

        <div class="info-points">
          <div class="point-item" v-if="scanQuestion">
            <span class="point-dot">●</span>
            <div class="point-text" v-html="scanQuestion"></div>
          </div>
          <div class="point-item" v-if="evaluationMethod">
            <span class="point-dot">●</span>
            <div class="point-text" v-html="evaluationMethod"></div>
          </div>
        </div>

        <button class="start-btn-mobile" @click="startTest">开始测评</button>
      </div>
      <div v-if="!startFlag">
        <div>
          <div class="start-facet" v-if="scaleForm.questionSort === 2 && scaleForm.showSortType === 1">
            <p>
              {{ scaleForm.scaleName }}
            </p>
          </div>
          <div style="margin-left: 20px" >
            <p class="start-question">{{ facetName }}</p>
          </div>
        </div>
        <div class="progress-container" v-if="scaleForm.scaleType === 1">
          <div class="progress-text"><span style="text-align: center">{{ currentIndex }}</span></div>
          <div class="rectangle">
            <el-progress :percentage="progressPercent" show-text="true" :format="formatProgress" trackColor="#AEE8FF"
                         color="#85DBFD" :stroke-width="12"
                         class="custom-progress" theme="line" />
          </div>
        </div>
        <div class="content" style="margin-top: 42px">
          <component
              :is="PsychologyTopic_1"
              :scaleId="scaleForm.scaleId"
              :questionSort="scaleForm.questionSort"
              :scaleType="scaleForm.scaleType"
              :data="dataList"
              :count="count"
              :footer="false"
              @submitTool="submitTool"
              @commitAll="commitAll"
              @getFacet="getFacet"
          />
        </div>
      </div>

      <!-- 结果页面 -->
      <div class="result-screen" v-if="showResult">
        <!-- 单维度结果 -->
        <div v-if="!showDescribe && scaleForm.questionSort === 1">
          <div class="psy-score">
            <div class="score-display">{{ sumScore }}</div>
            <div class="score-unit">分</div>
          </div>
          <button class="action-btn-mobile" @click="showDescription">查看结果</button>
        </div>

        <!-- 多维度结果显示得分 -->
        <div v-else-if="!showDescribe && scaleForm.questionSort === 2 && scaleForm.showSumScore === 1">
<!--          <div class="score-container">-->
<!--            <div style="display: flex;justify-content: center">-->
<!--              <img :src="psyScore" alt="" style="width: 50%;height: 50%">-->
<!--              <div class="score-display-muti">{{ sumScore ? sumScore : 0 }}<span class="score-unit-muti">分</span></div>-->
<!--            </div>-->
<!--          </div>-->
          <div class="score-container">
            <img :src="psyScore" alt="得分背景图" class="score-bg-img" />
            <div class="score-overlay">
              {{ sumScore ? sumScore : 0 }}<span class="score-unit-muti">分</span>
            </div>
          </div>
          <div>
            <div class="facet-results">
              <div class="facet-item" v-for="(option, index) in facetList" :key="index">
                <span class="facet-name">{{ option.facetName }}</span>
                <span class="facet-score">得分：{{ option.sumScore ? option.sumScore : 0 }}</span>
              </div>
            </div>
          </div>
          <div style="display: flex;justify-content: center">
            <button class="action-btn" @click="showDescription">查看结果</button>
          </div>
        </div>

        <!-- 多维度结果不显示得分 -->
        <div v-else-if="!showDescribe && scaleForm.questionSort === 2 && scaleForm.showSumScore === 2">
          <div class="facet-results">
            <div class="facet-item" v-for="(option, index) in facetList" :key="index">
              <span class="facet-name">{{ option.facetName }}</span>
              <span class="facet-score">得分：{{ option.sumScore ? option.sumScore : 0 }}</span>
            </div>
          </div>
          <div style="display: flex;justify-content: center">
            <button class="action-btn" @click="showDescription">查看结果</button>
          </div>
        </div>

        <!-- 结果描述 -->
        <div v-if="showDescribe" style="overflow: hidden;height: 100%;display: flex;flex-direction: column">
          <div class="result-description" >
            <div v-html="evaluateReference"></div>
          </div>
          <div class="action-buttons">
            <button class="action-btn" style="" @click="showScore">查看得分</button>
            <button class="action-btn" @click="restartTest">重新测评</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, defineProps} from "vue";
import useReader from "@/store/modules/reader.js";
import { getPhychology } from "@/api/book/reader";
import backgroundImage5 from "@/assets/images/readerResourcesIcon/psychology-background4-mobile.png";
import backgroundImage4 from "@/assets/images/readerResourcesIcon/psychology-background3-mobile.png";
import backgroundImage3 from "@/assets/images/readerResourcesIcon/psychology-background2.png";
import backgroundImage1 from "@/assets/images/readerResourcesIcon/psychology-background-mobile.png";
import backgroundImage2 from "@/assets/images/readerResourcesIcon/psychology-background1.png";
import PsychologyTopic_1 from "@/views/reader/sub/PageModule/PsychologyHealth/PsychologyTopicForMobile.vue";
import psychologyResultMobile from "@/assets/images/readerResourcesIcon/psychology-result-mobile.png";
import psychologyResultMobile2 from "@/assets/images/readerResourcesIcon/psychology-result-mobile2.png";
import psyScore from "@/assets/images/readerResourcesIcon/psy-score.png";

const store = useReader();
const route = useRoute();
const isShow = ref(false);
const startFlag = ref(true);
const showResult = ref(false);
const showDescribe = ref(false);
const scaleForm = ref({});
const dataList = ref([]);
const currentIndex = ref(1);
const sumScore = ref(0);
const count = ref(0);
const facetName = ref("");
const facetList = ref([]);
const scaleId = ref(route.query.scaleId);

const evaluationMethod = computed(() => scaleForm.value?.evaluationMethod || "");
const scanQuestion = computed(() => scaleForm.value?.scanQuestion || "");
const evaluateReference = computed(() => scaleForm.value?.evaluateReference || "");

const progressPercent = computed(() => {
  return Math.round((currentIndex.value / count.value) * 100);
})

const submitTool = (obj) => {
  currentIndex.value = obj + 1
};

const dialogStyle = computed(() => {
  console.log(!showDescribe && scaleForm.questionSort === 2 && scaleForm.showSumScore === 1)
  const baseStyle = {
    backgroundSize: '100% 100%',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    overflow: 'auto',
  };
  if (showResult.value && !showDescribe.value && scaleForm.value.questionSort === 2 && scaleForm.value.showSumScore === 1) {
    // return {
    //   ...baseStyle,
    //   backgroundImage: `url(${psychologyResultMobile2})`
    // };
    return {
      backgroundColor: '#85DBFD',
      width: '100vw',
      minHeight: '100vh',
      overflow: 'auto'
    };
  } else if (showResult.value && scaleForm.value.questionSort === 1 && !showDescribe.value) {
    return {
      ...baseStyle,
      backgroundImage: `url(${backgroundImage5})`
    };
  } else if (
      showResult.value &&
      scaleForm.value.questionSort === 2 &&
      scaleForm.value.showSumScore === 2 &&
      !showDescribe.value
  ) {
    return {
      ...baseStyle,
      backgroundImage: `url(${backgroundImage4})`
    };
  } else if (showDescribe.value) {
    return {
      ...baseStyle,
      backgroundImage: `url(${psychologyResultMobile})`
    };
  } else if (startFlag.value) {
    return {
      ...baseStyle,
      backgroundImage: `url(${backgroundImage1})`,
      backgroundAttachment: 'scroll'  // scroll更适合小屏
    };
  } else if (showResult.value) {
    if (scaleForm.value.questionSort === 2 && scaleForm.value.showSumScore === 1) {
      return {
        ...baseStyle,
        backgroundImage: `url(${backgroundImage2})`
      };
    }
  }

  // 默认样式
  return {
    backgroundColor: '#85DBFD',
    width: '100vw',
    minHeight: '100vh',
    overflow: 'auto'
  };
});

const startTest = () => {
  startFlag.value = false;
};

const restartTest = () => {
  startFlag.value = true;
  currentIndex.value = 1;
  sumScore.value = 0;
  showResult.value = false;
  showDescribe.value = false;
};

const showScore = () => {
  showResult.value = true;
  showDescribe.value = false;
};

const prevQuestion = () => {
  currentIndex.value--;
};

const formatProgress = (percentage) => {
  return `${currentIndex.value}/${count.value}`;
};

const showDescription = () => {
  showDescribe.value = true
}

const commitAll = async (obj) => {
  sumScore.value = obj.reduce((sum, item) => sum + item.score, 0);
  showResult.value = true;
  startFlag.value = true;
  facetList.value.forEach((facet) => {
    const list = dataList.value.filter((item) => item.facetId === facet.facetId);
    const questionIds = list.map((item) => item.questionId);
    const totalScore = obj
        .filter((item) => questionIds.includes(item.questionId))
        .reduce((sum, item) => sum + item.score, 0);
    facet.sumScore = totalScore;
  });
  console.log(dataList.value)
};

const getFacet = (obj) => {
  facetName.value = obj;
};

const getPsychologyHealth = async () => {
  const list = await getPhychology(scaleId.value);
  scaleForm.value = list.data.scale;
  if (scaleForm.value.questionSort === 1) {
    dataList.value = list.data.questionList;
  } else {
    dataList.value = list.data.questionList.flatMap(item =>
        item.moocPsychologyHealthScaleQuestion.map(facet => ({
          ...facet,
          facetName: item.facetName,
          facetId: item.facetId
        }))).sort((a, b) => a.questionSort - b.questionSort);
    facetName.value = list.data.questionList[0].facetName;
    facetList.value = list.data.questionList.map(item => ({
      facetName: item.facetName,
      facetId: item.facetId
    }))
  }
  count.value = dataList.value.length;


  // const list = await getPhychology(scaleId.value);
  // scaleForm.value = list.data.scale;
  // dataList.value = list.data.questionList;
  // count.value = dataList.value.length;
  console.log(dataList.value)
  console.log(scaleForm.value)
  console.log(count.value)
};

onMounted(() => {
  console.log(scaleId.value)
  getPsychologyHealth();
});
</script>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
}

.page-container {
  width: 100%;
  min-height: 100vh;
  background: white;
  position: relative;
}

.header-mobile {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-btn {
  font-size: 20px;
  margin-right: 12px;
  color: #1989fa;
  background: none;
  border: none;
  cursor: pointer;
}

.title-mobile {
  font-size: 18px;
  font-weight: 600;
}

.content-mobile {
  padding: 16px;
}

/* 开始页面样式 */
.start-screen {
  text-align: center;
  padding: 20px 0;
}

.scale-name-container {
  position: relative;
  margin: 0 auto;
  max-width: 90%;
}

.scale-name-mobile {
  position: relative;
  font-size: 22px;
  font-weight: bold;
  margin: 20px 0;
  color: #1471DC;
  word-break: break-word;
  padding: 15px;
  background: white;
  border: 2px solid #333;
  z-index: 2;
}

.overlap-box-mobile {
  position: absolute;
  top: 8px;
  left: 8px;
  right: -8px;
  bottom: -8px;
  background-color: #ADBCFF;
  border: 2px solid black;
  z-index: 1;
  border-radius: 4px;
}

.info-points {
  text-align: left;
  margin: 30px 0;
  padding: 0 10px;
}

.point-item {
  display: flex;
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.6;
}

.point-dot {
  color: #1471DC;
  margin-right: 10px;
  flex-shrink: 0;
  margin-top: 4px;
}

.point-text {
  flex: 1;
}

.start-btn-mobile {
  margin: 30px auto;
  width: 80%;
  max-width: 300px;
  height: 50px;
  font-size: 18px;
  border-radius: 25px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF);
  border: none;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

/* 结果页面样式 */
.result-screen {
  padding: 20px 0;
}

.score-display {
  font-weight: bold;
  color: #1471DC;
  margin: 20px 0 10px;
}

.score-display-muti {
  font-weight: bold;
  justify-self: center;
  color: #1471DC;
}

.score-unit {
  font-size: 20px;
  color: #1471DC;
}

.score-unit-muti {
  font-size: 20px;
  color: white;
}

.facet-results {
  margin: 20px 0;
}

.facet-item {
  display: flex;
  justify-content: space-between;
  background: white;
  padding: 15px;
  margin: 10px 0;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  height: 100px;
}

.facet-name {
  width: 80%;
  font-weight: bold;
  white-space: normal;
  justify-self: flex-start;
  overflow-wrap: break-word;
  overflow: auto;
}

.facet-score {
  width: 20%;
  color: #1471DC;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.action-btn {
  padding: 12px 25px;
  border-radius: 25px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF);
  color: white;
  border: none;
  font-size: 16px;
  font-weight: bold;
  min-width: 140px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.action-btn-mobile {
  padding: 12px 25px;
  border-radius: 25px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF);
  color: white;
  border: none;
  font-size: 16px;
  font-weight: bold;
  min-width: 140px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.result-description {
  width: 100%;
  height:420px;
  padding: 0px 20px;
  display: flex;
  overflow: auto;
  margin-top: 45%;
}

/* 响应式调整 */
@media (max-width: 360px) {
  .content-mobile {
    padding: 10px;
  }

  .scale-name-mobile {
    font-size: 18px;
  }

  .point-item {
    font-size: 14px;
  }

  .question-content-mobile {
    font-size: 16px;
  }
}



.progress-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  margin-right: 5%;
  z-index: -1;
  flex-wrap: wrap;
}

.progress-text {
  border: black solid 2px;
  border-radius: 50%;
  background-color: #5BCBFF;
  width: 10vw;
  height: 10vw;
  color: black;
  font-size: 4vw;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
  position: relative;
  max-width: 50px;
  max-height: 50px;
  top:1vh;
  left: 15vw;
}

.rectangle {
  height: 8vw;
  width: 60vw;
  background-color: #D4F3FF;
  margin-left: 6vw;
  border: 2px solid black;
  z-index: 2;
  border-radius: 0 5vw 5vw 0;
  position: relative;
  justify-content: center;
  top: 1vw;
}

.custom-progress {
  margin-top: 1.2vh;
  z-index: 4;
  font-size: 4vw;
  margin-left: 10%;
  margin-right: 5%;
}

.score-container {
  position: relative;
  width: 70%;
  max-width: 350px;
  margin: 0 auto;
}

.score-bg-img {
  width: 100%;
  height: auto;
  display: block;
}

.score-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 50px;
  font-weight: bold;
  color: #1471DC;
  text-align: center;
  white-space: nowrap;
  color: white;
}

@media screen and (device-width: 768px) and (orientation: portrait),
screen and (device-width: 1024px) and (orientation: landscape),
screen and (min-width: 768px) and (max-width: 1112px) {
  .content-mobile {
    padding: 32px;
  }

  .scale-name-mobile {
    font-size: 26px;
    padding: 20px;
  }

  .point-item {
    font-size: 18px;
    margin-bottom: 25px;
  }

  .start-btn-mobile {
    font-size: 20px;
    height: 55px;
    max-width: 320px;
  }

  .result-description {
    width: 100%;
    height:470px;
    padding: 0px 20px;
    display: flex;
    overflow: auto;
    margin-top: 30%;
  }

  .score-unit {
    font-size: 30px;
    color: #1471DC;
    margin-bottom: 0px;
  }

  .psy-score {
    justify-content: center;
    align-items: center;
    display: flex;
    margin-top: 60%;
    text-align: center;
  }

  .progress-text {
    width: 10vw;
    height: 10vw;
    font-size: 4vw;
    max-width: 60px;
    max-height: 60px;
    left: 10vw;
  }

  .rectangle {
    height: 8vw;
    width: 65vw;
    border-radius: 4vw;
    top: 1vh;
    left: -5vw;
  }

  .custom-progress {
    font-size: 4vw;
    margin-left: 10vw;
    margin-right: 4vw;
    top: 1vh;
  }

  .score-display {
    font-size: 100px !important;
    margin-top: -10%;
  }

  .score-display-muti {
    font-size: 100px !important;
    margin-top: 17%;
  }

  .facet-item {
    font-size: 20px;
    padding: 18px;
  }

  .result-description {
    font-size: 18px;
    padding: 24px;
  }

  .action-btn {
    font-size: 18px;
    padding: 16px 32px;
    min-width: 160px;
  }

  .action-buttons {
    gap: 24px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 28%;
  }
  .progress-container {
    margin-top: 20px;
    justify-content: center;
    gap: 20px;
  }

  .action-btn-mobile {
    padding: 12px 25px;
    border-radius: 25px;
    background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF);
    color: white;
    border: none;
    font-size: 16px;
    font-weight: bold;
    min-width: 140px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    margin-top: 40%;
  }

  .facet-results {
    margin-top: 20%;
  }
}

@media (max-width: 480px) {
  .progress-text {
    width: 10vw;
    height: 10vw;
    font-size: 6vw;
    left: 16vw;
  }

  .rectangle {
    height: 10vw;
    width: 60vw;
    top: 1vh;
    border-radius: 7vw 7vw 7vw 7vw;
  }

  .custom-progress {
    font-size: 6vw;
    margin-left: 10vw;
    margin-right: 0;
  }

  .action-btn-mobile {
    padding: 12px 25px;
    border-radius: 25px;
    background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF);
    color: white;
    border: none;
    font-size: 16px;
    font-weight: bold;
    min-width: 140px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    margin-top: 80%;
  }

  .scale-name-mobile {
    font-size: 20px;
  }

  .psy-score {
    justify-content: center;
    align-items: center;
    display: flex;
    margin-top: 70%
  }

  .score-display {
    font-size: 70px;
  }

  .score-display-muti {
    font-size: 80px !important;
    margin-top: 30%;
  }

  .score-unit {
    margin-bottom: -40px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    margin-top: 25%;
  }

  .action-btn {
    width: 80%;
  }
  .facet-results {
    margin-top: 10%;
  }
}


</style>
