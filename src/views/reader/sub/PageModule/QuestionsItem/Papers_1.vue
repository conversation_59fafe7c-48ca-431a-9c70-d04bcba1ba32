<!-- 试卷--考试 -->
<style lang="scss" scoped>
.Papers {
  width: 100%;
  height: 55px;
  background-image: url("@/assets/images/readerResourcesIcon/modelBackground.png");
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  font-size: 16px;
  margin: 10px 0;
  .collapse-main {
    display: flex;
    align-items: center;
    .icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
     
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
     
      font-size: 16px;
      text-align: left;
    }
    .name {
      flex: 1;
      padding:0 10px;
    }
  }
}
.PapersContent {
  width: 100%;
  height: 80vh;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 5px;
  .PapersContentItem {
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .content {
      width: 100%;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  .fraction {
    position: fixed;
    top: 15vh;
    right: 20vw;
    font-size: 50px;
    color: #f00;
    text-decoration: underline double;
  }
}

.__name__ {
  font-size: 16px;
  margin-right: 3px;
  cursor: pointer;
}
</style>

<template>
  <div class="Papers __orderTemplateBgUrl__" v-if="!uuid">
    <div class="collapse-main">
      <div class="icon">
        <img :src="templateStyle.theme == 'light' ? lightIcon : darkIcon" />
      </div>

      <div class="name" :style="{color: templateStyle.theme == 'light' ? '#fff' : '#333'}">
        {{ paperInfo.paperTitle }}
      </div>
    </div>
    <div class="__name__" :style="{color: templateStyle.theme == 'light' ? '#fff' : '#333'}"  @click="previewResource">打开</div>
  </div>

  <el-dialog
    v-model="isShow"
    :append-to-body="true"
    :title="paperInfo.paperTitle"
    width="75%"
    top="20px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="closeHandler"
  >
    <main class="PapersContent" :key="keys">
      <div
        class="PapersContentItem"
        v-for="(ele, i) in papersData"
        :key="ele.collectionId"
      >
        <!-- {{ NUM[i] }}、{{ TYPE[ele.questionType - 1] }} -->
        <div v-if="ele.questionType != 8">
          <div class="title">
           {{
              TYPE[ele.questionType - 1] === "排序题"
                ? TYPE[ele.questionType - 1] +
                  "提示：请对以下排序题进行操作，将选项拖拽至正确的位置。"
                : TYPE[ele.questionType - 1]
            }}
          </div>
          <div class="content">
            <component
              v-for="item in ele.questionList"
              :key="item.collectionId"
              :is="COMPONENTSLIST[ele.questionType - 1]"
              :OPTION="OPTION"
              :TYPE="TYPE"
              :ableToResubmit="ableToResubmit"
              :data="item.questionsData"
              :footer="false"
              :statusq="status"
              @submitTool="(obj) => submits.push(obj)">
            </component>
          </div>
        </div>
      </div>
      <!-- <div class="fraction"
        v-show="fraction && !uuid"> -->
      <div class="fraction"
        v-show="fraction/1>=0&&!status">
        {{ fraction }}
      </div>
    </main>

    <template #footer>
      <el-button :disabled="!fraction" @click="reset" v-if="resetStatus"
        >重置</el-button
      >
      <el-button @click="isShow = false" v-else>取消</el-button>

      <el-button
        type="primary"
        @click="submitThePaper"
        :disabled="fraction || !status">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import lightIcon from "@/assets/resources/light/testPaper.svg";
import darkIcon from "@/assets/resources/dark/testPaper.svg";
import { ref, defineProps, watch, onMounted } from "vue";
import {
  getTestPaperQuestions,
  addPapersAnswer,
  getPaperAnswer,
} from "@/api/book/reader";
import Topic_1 from "./Topic_1.vue";
import Topic_2 from "./Topic_2.vue";
import Topic_3 from "./Topic_3.vue";
import Topic_4 from "./Topic_4.vue";
import Topic_5 from "./Topic_5.vue";
import Topic_6 from "./Topic_6.vue";
import useReader from "@/store/modules/reader";
import { ElMessageBox } from "element-plus";
import { unregisterKeyboardEvent, registerKeyboardEvent, broadCastChannel, TEST_PAPER_SUBMIT_EVENT } from "@/utils/reader";
const ableToResubmit = ref(true);
const store = useReader();
const { proxy } = getCurrentInstance();
const COMPONENTSLIST = [
  Topic_1,
  Topic_2,
  Topic_3,
  Topic_4,
  Topic_5,
  Topic_6,
  Topic_1,
];
const NUM = ["一", "二", "三", "四", "五", "六", "七"];
const TYPE = [
  "单选题",
  "多选题",
  "填空题",
  "排序题",
  "连线题",
  "简答题",
  "判断题",
];
const OPTION = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "G",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];
const props = defineProps({
  id: String,
  paperInfo: Object,
  uuid: String,
  chapterId: String,
  resetStatus: Boolean
})
const fraction = ref(0) //分数
const keys = ref(1)
const isShow = ref(false)
const papersData = ref([])
const submits = ref([]) //小题组件所有的提交事件
const status = ref(false)
//作业列表打开弹窗
watch(
  () => props.uuid,
  () => {
    previewResource();
  }
);
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  return obj;
});

const reset = () => {
  papersData.value = papersData.value.map((ele) => ({
    ...ele,
    questionList: ele.questionList.map((item) => ({
      ...item,
      questionsData: { ...item.questionsData, defaultValue: undefined },
    })),
  }));
  keys.value++;
  fraction.value = 0;
};
/**
 * 监测字符串是否为富文本
 */
function isRichText(str) {
  const div = document.createElement("div");
  div.innerHTML = str;
  const _s = div.children.length > 0 || div.childNodes.length > 1;
  div.remove();
  return _s;
}

/**
 * 向题干开头和结尾添加小题序号和分值
 */
const addserialNumber = (num, str, branch) => {
  let _t = "";
  if (isRichText(str)) {
    const div = document.createElement("div");
    div.innerHTML = str;
    // div.children[0].insertAdjacentHTML("afterbegin", `${num}、`);
    div.children[0].insertAdjacentHTML("beforeend", `(${branch}分)`);
    _t = div.innerHTML;
    div.remove();
  } else {
    _t = `${str}(${branch}分)`;
  }
  return _t;
};

const previewResource = async () => {
  submits.value = []
  registerKeyboardEvent(
    "quizInReader",
    (e) => {
      // 由于阅读器注册了左键和右键进行翻页，当弹窗出现的时候，弹窗注册左键和右键，并停止世界冒泡，用于区分变速器，查看上一页和下一页。
      // 其他弹窗同理
      if (e.keyCode === 39 || e.keyCode === 37) {
        e.cancelBubble = true;
      }
    },
    {
      before: "reader",
    }
  );
  let lastSaveAnswer = [];
  fraction.value = 0;
  const saveData = await getPaperAnswer({
    chapterId: props.chapterId,
    bookId: store.comprehensiveBookData.bookId,
    paperId: props.paperInfo.paperId,
    bookPaperId: props.paperInfo.bookPaperId,
  });
  // 如果是打开
  if (saveData.data) {
    lastSaveAnswer = saveData.data.dtbBookTestPaperDetailList
    saveData.data.dtbBookTestPaperDetailList.forEach(ele => (fraction.value += ele.questionScore))
    status.value = false
  } else {
    status.value = true
  }
  const res = await getTestPaperQuestions(props.paperInfo.paperId);
  if (res.code === 200) {
    // 遍历试题
    papersData.value = res.data.map((ele) => {
      return {
        ...ele,
        questionList: ele.questionList.map((item, index) => {
          const val = item.questionContent;
          const lastSaveAnswerItem = lastSaveAnswer.find(
            (_e) => _e.questionId === item.questionId
          );
          return {
            ...item,
            questionsData: {
              questionText: TYPE[val.questionType - 1],
              questionType: val.questionType,
              questionContent: addserialNumber(
                index + 1,
                val.questionContent,
                item.questionScore
              ),
              analysis: val.analysis,
              options: val.options,
              rightAnswer: val.rightAnswer,
              questionScore: item.questionScore, //分值
              questionId: item.questionId,
              defaultValue: lastSaveAnswerItem?.answerContent
            }
          }
        })
      }
    })
    isShow.value = true
  }
};
function closeHandler() {
  unregisterKeyboardEvent("quizInReader");
}
/**
 * 试卷提交
 */
const submitThePaper = () => {

  const params = {
    chapterId: props.chapterId,
    bookId: store.comprehensiveBookData.bookId,
    paperId: props.paperInfo.paperId,
    bookPaperId: props.paperInfo.bookPaperId,
    dtbBookQuestionAnswerList: [],
    domId: props.id,
  };
  const subjectStatus = []; //记录所有题目的状态,是否有没答的题目
  submits.value.forEach((ele) => {
    subjectStatus.push(ele.validation());
  });

  if (!subjectStatus.includes(false)) {
    let _f = 0; //分数
    submits.value.forEach((ele) => {
      const element = ele.getData();
      _f += element.score;

      params.dtbBookQuestionAnswerList.push(element.dtbBookQuestionAnswer);
    });

    if (!ableToResubmit.value){
      submits.value.forEach((ele) => {
        ele.onAnalysis();
      });
      fraction.value = _f + "";
    }else{
      addPapersAnswer(params).then((res) => {
        if (res.code === 200) {
          submits.value.forEach((ele) => {
            ele.onAnalysis();
          });
          fraction.value = _f + "";
          broadCastChannel(TEST_PAPER_SUBMIT_EVENT, {});
        }
      });
    }

    addPapersAnswer(params).then(res => {
      if (res.code === 200) {
        status.value = false
        submits.value.forEach(ele => {
          ele.onAnalysis()
        })
        fraction.value = _f + ''
        broadCastChannel(TEST_PAPER_SUBMIT_EVENT, {});
      }
    })
  } else {
    ElMessageBox.confirm("当前有未作答题目，是否进行提交", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        let _f = 0; //分数
        submits.value.forEach((ele) => {
          const element = ele.getData();
          _f += element.score;

          params.dtbBookQuestionAnswerList.push(element.dtbBookQuestionAnswer);
        });

        if (!ableToResubmit.value){
          submits.value.forEach((ele) => {
            ele.onAnalysis();
          });
          fraction.value = _f + "";
        }else{
          addPapersAnswer(params).then(res => {
          if (res.code === 200) {
            status.value = false
            submits.value.forEach(ele => {
              ele.onAnalysis()
            })
            fraction.value = _f + ''
            broadCastChannel(TEST_PAPER_SUBMIT_EVENT, {});
          }
        })
        }
      })
      .catch(() => {});
  }
};

onMounted(() => {
  const simplified = inject("simplifiedReadingMode");
  if (simplified) {
    ableToResubmit.value = false;
  }
});
</script>
