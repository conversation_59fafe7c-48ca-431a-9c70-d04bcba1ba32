<!-- 连线 -->
<style lang="scss" scoped>
.questionType {
  color: #333;
  margin-bottom: 16px;
 
  ::v-deep(img) {
    max-width: 100%;
  }

  .questionContent {
    font-size: 14px;
    color:#333;
    padding-bottom: 10px;
    border-bottom: 1px solid #E5E6E7;
    line-height: 28px;
    ::v-deep(img) {
      max-width: 100%;
    }
  }

  .stem {
    margin: 10px 0;

   
    :deep(p){
      line-height: 28px;
    }
    ::v-deep(img) {
      max-width: 100%;
    }
  }

  .answer {
    position: relative;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .left,
    .right {
      width: 240px;
      flex-shrink: 0;
    }

    .centers {
      flex: 1;
      height: 100%;
    }
    cursor: pointer;
    .leftItem,
    .rightItem {
      border: 1px solid #E5E6E7;
      border-radius: 8px;
      line-height: 25px;
      box-sizing: border-box;
      margin: 10px 0;
      position: relative;
      padding: 0 2px;
      transition: 0.3s;
      height: 89px;
      word-break: break-all;
      font-size: 14px;
      

      .content {
        padding: 0 20px;


        line-height: 25px;

        &::-webkit-scrollbar {
          width: 10px;
        }

        p {
          padding: 10px;
        }
      }

      .anchorPoint {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .rightItemHover:hover {
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }

    .leftItem {
      .anchorPoint {
        right: -12px;
        background-color: #fff;
        cursor: pointer;
        border-radius: 50%;
      }

      .leftContent {
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        padding: 5px;

        .leftImg {
          margin:5px 0;
          width: 80px;
          flex-shrink: 0;
          height: 60px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 100%;
            height: 60px;
          }

          video {
            width: 100%;
            height: 60px;
          }
        }

        .leftText {
          flex: 1;
          padding: 0 10px;

        }
      }

    }

    .rightItem {
      .anchorPoint {
        left: -8px;
        width: 15px;
        height: 15px;
        background-color: #666;
        border-radius: 50%;
      }

      .leftContent {
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        padding: 5px;

        .leftImg {
          width: 80px;
          margin:5px 0;
          flex-shrink: 0;
          height: 60px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 100%;
            height: 60px;
          }

          video {
            width: 100%;
            height: 60px;
          }
        }

        .leftText {
          flex: 1;
          padding: 0 10px;

        }
      }
    }

    .maskLayer {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      cursor: not-allowed;
    }
  }

  .analysis {
    .analysisItem {
      display: flex;
      align-items: center;
      margin: 10px 0;


      .label {
        margin: 0 16px;
      }


    }

    .question-hint {
      ::v-deep(img) {
        max-width: 100%;
      }

    }
  }

  ::v-deep(.el-tabs__item.is-active) {
    color: #0966B4;
  }

  ::v-deep(.el-tabs__active-bar) {
    background: #0966B4;
  }


  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }

  .footer {
    text-align: center;

    button {
      margin-right: 10px;
    }
  }
}
</style>

<template>
  <section class="questionType" >
    <div class="questionContent" v-if="data.questionRemark && data.questionRemark != '<p><br></p>' && data.questionRemark !=test" v-html="data.questionRemark" style="font-weight: bold;"></div>
    <div class="stem" v-html="data.questionContent" :style="{borderBottom:!data.questionRemark && data.questionRemark == '<p><br></p>'?'1px solid #E5E6E7':'none',fontWeight:!data.questionRemark && data.questionRemark == '<p><br></p>'?'bold':'normal'}"></div>
    <div class="answer" ref="stemBoxRefs">
      <div class="left">
        <div class="leftItem" v-for="ele in originalData.left" :key="ele.optionId" ref="leftItemRefs"
          :style="{ height: data.optionHeight + 'px' }" @scroll.stop>
          <div class="leftContent" >
            <OptionContentTemplate :optionContent="ele.optionContent" :optionHeight="data.optionHeight"/>     
          </div>
          <el-icon class="anchorPoint" size="25" @click.stop="startDrawingLines(ele)">
            <CirclePlus />
          </el-icon>
        </div>
      </div>
      <div class="centers" ref="canvasRefs">&nbsp;</div>
      <div class="right">
        <div :class="`rightItem ${drawingLines.isDrawing ? 'rightItemHover' : ''}`" v-for="ele in originalData.right"
          :key="ele.optionId" ref="rightItemRefs" @click="clickRightItem(ele)" @mouseenter="mouseenterRightItem(ele)"
          :style="{ height: data.optionHeight + 'px' }">
          <div class="leftContent" >
            <OptionContentTemplate :optionContent="ele.optionContent" :optionHeight="data.optionHeight" />
          </div>
          <div class="anchorPoint"></div>
        </div>
      </div>
      <div class="maskLayer" v-show="status.status"></div>
    </div>
    <div class="analysis" v-show="status.status">
      <div class="analysisItem">
        <div>
          我的答案：{{ removeDuplicates(status.myAnswerText) || "未作答" }}
        </div>
        <div style="padding-left: 16px; box-sizing: border-box"></div>
        &nbsp;
        <el-icon color="#70cd79" v-if="status.isCorrect">
          <CircleCheckFilled />
        </el-icon>
        <el-icon color="#f57878" v-else>
          <CircleCloseFilled />
        </el-icon>
      </div>
      <div class="analysisItem">正确答案：{{ status.rightKeyText }}</div>
      <question-hint v-if="status.status" :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"></question-hint>
    </div>
    <div class="footer" v-if="footer">
      <el-button @click="redo" :disabled="!status.status" size="large" style="width: 100px; height: 36px">重做</el-button>
      <el-button type="primary" @click="submit" size="large" :disabled="status.status" color="#0966b4"
        style="width: 100px; height: 36px">提交</el-button>
    </div>
  </section>
</template>

<script setup>
import OptionContentTemplate from "./optionContentTemplate.vue"
import {
  ref,
  reactive,
  defineProps,
  onMounted,
  nextTick,
  onBeforeUnmount,
  defineEmits,
} from "vue";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  CirclePlus,
} from "@element-plus/icons-vue";
import { addQuestionAnswer } from "@/api/book/reader";
import { uuid } from "@/utils/index";
import useReader from "@/store/modules/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data } = defineProps({
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
});
const canvasRefs = ref(null);

const ctx = ref(null);
const stemBoxRefs = ref(null);
const canvasDom = ref(null);
const leftItemRefs = ref([]);
const rightItemRefs = ref([]);
const status = reactive({
  status: false,
  rightKey: [], //正确答案
  rightKeyText: "", //正确答案文字描述
  myAnswer: [], //我的答案
  myAnswerText: "", //我的答案文字描述
  isCorrect: false,
});
const drawingLines = reactive({
  isDrawing: false, //当前是否正在绘制
  y1: 0,
  line: [], //{ startY: 0, endY: 0 }
});
const originalData = reactive({
  left: [],
  right: [],
});

const redo = () => {
  status.isCorrect = false;
  status.rightKeyText = "";
  status.myAnswerText = "";
  status.myAnswer = [];
  status.status = false;
  status.isCorrect = false;
  drawingLines.line = [];
  ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
};
const test = ref('<p style="line-height: 2;"><br></p>')
function removeDuplicates(input) {
  // 按 "；" 分割字符串为数组
  const items = input.split("；");

  // 使用 Set 去重
  const uniqueItems = [...new Set(items)];

  // 将去重后的数组重新用 "；" 连接为字符串
  return uniqueItems.join("；");
}

const submit = async () => {
  if (!status.myAnswer.length) return proxy.$message.error("请连线后提交");
  // 过滤掉未完成的连线（target为null）
  const validMyAnswer = status.myAnswer.filter(
    (item) => item.hasOwnProperty("source") && item.hasOwnProperty("target")
  );

  const sort_rightKey = status.rightKey.sort((a, b) => a.source - b.source);
  // const sort_myAnswer = status.myAnswer.sort((a, b) => a.source - b.source)
  const sort_myAnswer = validMyAnswer.sort((a, b) => a.source - b.source);
  if (
    JSON.stringify(sort_rightKey) ===
    JSON.stringify(
      sort_myAnswer.map((sort) => {
        return {
          ...sort,
          key: undefined,
        };
      })
    )
  ) {
    status.isCorrect = true;
  }
  let rightKeyText = "";
  sort_rightKey.forEach((ele) => {
    rightKeyText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.rightKeyText = rightKeyText;

  // 开始
  // 修复：使用 Set 确保唯一性
  let uniquePairs = new Set();
  sort_myAnswer.forEach((ele) => {
    const pair = `左${ele.source + 1}----右${ele.target + 1}`;
    //临时性处理NaN的问题 非源头解决 但也没啥问题
    if (!pair.includes("NaN")) uniquePairs.add(pair);
  });
  status.myAnswerText = Array.from(uniquePairs).join(" ；");
  // 结束
  // let myAnswerText = ''
  // sort_myAnswer.forEach(ele => {
  //   myAnswerText += `左${ele.source + 1}----右${ele.target + 1} ；`
  // })
  // status.myAnswerText = myAnswerText
  const params = {
    ...data.params,
    answerContent: JSON.stringify(validMyAnswer),
    score: status.isCorrect ? 100 : 0,
  };

  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

/**
 * 开始划线
 */
const startDrawingLines = (val) => {
  console.log(val)
  drawingLines.y1 = val.y;
  drawingLines.isDrawing = true;
  status.myAnswer.push({ source: val.index, target: null });
  drawingLines.line.push({ startY: val.y, endY: 0 });
};

/**
 * 选中右侧
 */
const clickRightItem = (val) => {
  const key = uuid();
  const lastItem = status.myAnswer.pop();
  status.myAnswer.push({ ...lastItem, target: val.index, key });
  const lineItem = drawingLines.line.pop();
  drawingLines.line.push({ ...lineItem, endY: val.y, key });
  drawingLines.isDrawing = false;
};

/**
 * 判断当前鼠标的所属位置在不在canvas的线上
 */
const containsPoint = (x, y) => {
  let arr = [];
  drawingLines.line.forEach((ele) => {
    let x1 = 0;
    let y1 = ele.startY;
    let x2 = canvasDom.value.width;
    let y2 = ele.endY;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy); // 线段长度
    const u = ((x - x1) * dx + (y - y1) * dy) / (length * length); // 点到线的投影比例
    const ix = x1 + u * dx; // 投影点的x坐标
    const iy = y1 + u * dy; // 投影点的y坐标
    const ix_diff = ix - x; // 投影点与点击点的x差值
    const iy_diff = iy - y; // 投影点与点击点的y差值
    // 检查距离是否小于某个阈值（例如5px）
    if (Math.sqrt(ix_diff * ix_diff + iy_diff * iy_diff) < 5) {
      arr.push({ ...ele });
    }
  });
  return arr;
};

/**
 * canvas的鼠标滑过事件
 */
const mousemoveLine = (event) => {
  if (drawingLines.isDrawing) {
    // 清除之前的线段，并绘制跟随鼠标的线段
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine(0, drawingLines.y1, event.offsetX, event.offsetY);
  } else {
    const rect = canvasDom.value.getBoundingClientRect(); // 获取Canvas的绝对位置和尺寸
    const x = event.clientX - rect.left; // 点击的x坐标相对于Canvas的x坐标
    const y = event.clientY - rect.top; // 点击的y坐标相对于Canvas的y坐标
    const [line] = containsPoint(x, y);
    if (line) {
      ctx.value.beginPath();
      ctx.value.moveTo(0, line.startY);
      ctx.value.lineTo(canvasDom.value.width, line.endY);
      ctx.value.strokeStyle = "red"; // 根据isHovering改变颜色
      ctx.value.stroke();
      canvasDom.value.style.cursor = "pointer";
    } else {
      ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
      drawLine();
      canvasDom.value.style.cursor = "default";
    }
  }
};
const mouseleaveLine = () => {
  if (!drawingLines.isDrawing) {
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine();
    canvasDom.value.style.cursor = "default";
  }
};

/**
 * canvas线的鼠标点击事件
 *
 */

const clickLine = (e) => {
  if (drawingLines.isDrawing) return;
  const rect = canvasDom.value.getBoundingClientRect(); // 获取Canvas的绝对位置和尺寸
  const x = e.clientX - rect.left; // 点击的x坐标相对于Canvas的x坐标
  const y = e.clientY - rect.top; // 点击的y坐标相对于Canvas的y坐标
  const [line] = containsPoint(x, y);
  if (line) {
    drawingLines.line = drawingLines.line.filter((ele) => ele.key !== line.key);
    status.myAnswer = status.myAnswer.filter((ele) => ele.key !== line.key);
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine();
  }
};

const mouseenterRightItem = (val) => {
  if (drawingLines.isDrawing) {
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine(0, drawingLines.y1, canvasDom.value.width, val.y);
  }
};

function drawLine(x1, y1, x2, y2) {
  ctx.value.beginPath();
  drawingLines.line.forEach((ele) => {
    if (ele.endY) {
      ctx.value.moveTo(0, ele.startY);
      ctx.value.lineTo(canvasDom.value.width, ele.endY);
    }
  });
  if (y1) {
    ctx.value.moveTo(x1, y1);
    ctx.value.lineTo(x2, y2);
  }
  ctx.value.strokeStyle = "#333";
  ctx.value.stroke();
}

const init = () => {
  originalData.left = data.options.filter((ele) => ele.optionPosition === 1);
  originalData.right = data.options.filter((ele) => ele.optionPosition === 2);
  status.rightKey = JSON.parse(data.rightAnswer);
  nextTick(() => {

    originalData.left = originalData.left.map((ele, i) => {
      //  判断字符长度是否小于12，小于12则居中显示
     
      return {
        ...ele,
        y:
          leftItemRefs.value[i].offsetTop +
          leftItemRefs.value[i]?.offsetHeight / 2,
        index: i,
      };
    });
    originalData.right = originalData.right.map((ele, i) => {
      //  判断字符长度是否小于12，小于12则居中显示
   
      return {
        ...ele,
        y:
          rightItemRefs.value[i].offsetTop +
          rightItemRefs.value[i].offsetHeight / 2,
        index: i,
      };
    });
    canvasDom.value = document.createElement("canvas");
    ctx.value = canvasDom.value.getContext("2d");
    canvasDom.value.width = canvasRefs.value.offsetWidth;
    canvasDom.value.height = stemBoxRefs.value.offsetHeight;
    canvasRefs.value.innerHTML = "";
    canvasRefs.value.appendChild(canvasDom.value);
    canvasDom.value.addEventListener("mousemove", mousemoveLine);
    canvasDom.value.addEventListener("mouseleave", mouseleaveLine);
    canvasDom.value.addEventListener("click", clickLine);
    if (data.defaultValue) {
      status.myAnswer = JSON.parse(data.defaultValue);
      drawingLines.line = status.myAnswer.map((ele) => {
        return {
          startY: originalData.left[ele.source - 0].y,
          endY: originalData.right[ele.target - 0].y,
        };
      });
      onAnalysis();
      drawLine();
    }
  });
};

const validation = () => !!status.myAnswer.length;
const getData = () => {
  const validMyAnswer = status.myAnswer.filter(
    (item) => item.hasOwnProperty("source") && item.hasOwnProperty("target")
  );

  const sort_rightKey = status.rightKey.sort((a, b) => a.source - b.source);
  // const sort_myAnswer = status.myAnswer.sort((a, b) => a.source - b.source)
  const sort_myAnswer = validMyAnswer.sort((a, b) => a.source - b.source);
  const new_sort_myAnswer = sort_myAnswer.map(({ key, ...rest }) => rest);

  if (JSON.stringify(sort_rightKey) === JSON.stringify(new_sort_myAnswer)) {
    status.isCorrect = true;
  }
  let rightKeyText = "";
  sort_rightKey.forEach((ele) => {
    rightKeyText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.rightKeyText = rightKeyText.slice(0, -1);
  // 开始
  // 修复：使用 Set 确保唯一性
  let uniquePairs = new Set();
  sort_myAnswer.forEach((ele) => {
    const pair = `左${ele.source + 1}----右${ele.target + 1}`;
    //处理NaN的问题
    if (!pair.includes("NaN")) uniquePairs.add(pair);
  });
  status.myAnswerText = Array.from(uniquePairs).join(" ；");
  // 结束
  let myAnswerText = ''
  sort_myAnswer.forEach(ele => {
    myAnswerText += `左${ele.source + 1}----右${ele.target + 1} ；`
  })
  status.myAnswerText = myAnswerText
  return {
    dtbBookQuestionAnswer: {
      answerContent: JSON.stringify(validMyAnswer),
      questionId: data.questionId,
      score: status.isCorrect ? 100 : 0,
    },
    score: status.isCorrect ? data.questionScore : 0, //分值
  };
};


const onAnalysis = () => {
  getData();
  status.status = true;
};
onMounted(() => {
  init();
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  
});

onBeforeUnmount(() => {
  canvasDom.value.removeEventListener("mousemove", mousemoveLine);
  canvasDom.value.removeEventListener("mouseleave", mouseleaveLine);
  canvasDom.value.removeEventListener("click", clickLine);
});
</script>
