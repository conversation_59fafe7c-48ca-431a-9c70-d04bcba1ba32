<!-- 表格 -->
<style lang="scss" scoped>
.TablePlus {
  margin: 15px 0;

  .header {
    text-align: center;
    font-size: 1.125rem;
    margin-bottom: 10px;
  }

  .tableMain {
    width: 100%;
    max-width: 690px;
    overflow-x: auto;
   

    th {
      padding: 5px 0;
    }
    td {
      padding: 5px 0;
      word-break: break-all;
    }
  }
}
</style>
<style lang="scss">
.__tables_default {
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  //text-align: center;
  width: 100%;
  font-size: 1.125rem;

  th {
    // background-color: #f1f3f5;
    font-weight: bold;
    min-height:36px;
  }

  td {
    
  }
}

.__tables_3,
.__tables_2,
.__tables_1 {
  border: 1px solid #DEE0E3;
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  text-align: center;
  width: 100%;
  font-size: 14px;
  word-break: break-all;
  th {
    border: 1px solid #DEE0E3;
    font-weight: bold;
  }

  td {
    border: 1px solid #DEE0E3;
   
  }
}

.__tables_4 {
  border: 1px solid #DEE0E3;
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  text-align: center;
  width: 100%;
  font-size: 14px;

  th {
    border: 1px solid #DEE0E3;
    font-weight: bold;
  }

  td {
    border: 1px solid #DEE0E3;
  
  }
}

table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%; 
  margin: 0;
  overflow-x: scroll;
  page-break-inside: auto;
  ::-webkit-scrollbar {
    display: block !important;
  }
  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  // thead {
  //   display: table-header-group;
  // }
  td,
  th {
    min-width: 1em;
    border: 1px solid var(--umo-content-table-border-color);
    padding: 3px 5px;
    vertical-align: middle;
    min-height: 36px;
    box-sizing: border-box;
    position: relative;

    > * {
      margin-bottom: 0;
    }

    &[data-align="left-top"] {
      vertical-align: top;
      text-align: left;
    }

    &[data-align="center-top"] {
      vertical-align: top;
      text-align: center;
    }

    &[data-align="right-top"] {
      vertical-align: middle;
      text-align: right;
    }

    &[data-align="justify-top"] {
      vertical-align: middle;
      text-align: justify;
    }

    &[data-align="left-middle"] {
      vertical-align: middle;
      text-align: left;
    }

    &[data-align="center-middle"] {
      vertical-align: middle;
      text-align: center;
    }

    &[data-align="right-middle"] {
      vertical-align: middle;
      text-align: right;
    }

    &[data-align="justify-middle"] {
      vertical-align: middle;
      text-align: justify;
    }

    &[data-align="left-bottom"] {
      vertical-align: bottom;
      text-align: left;
    }

    &[data-align="center-bottom"] {
      vertical-align: middle;
      text-align: center;
    }

    &[data-align="right-bottom"] {
      vertical-align: bottom;
      text-align: right;
    }

    &[data-align="justify-bottom"] {
      vertical-align: bottom;
      text-align: justify;
    }
  }

  th {
    font-weight: bold;
    text-align: left;
    
  }

  .selectedCell:after {
    z-index: 2;
    position: absolute;
    content: "";
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
   
    pointer-events: none;
  }

  .column-resize-handle {
    position: absolute;
    right: -1px;
    top: 0;
    bottom: -1px;
    width: 3px;
    background-color: var(--umo-primary-color);
    pointer-events: none;
  }

  p {
    margin: 0;
  }
}
</style>
<template>
  <div class="TablePlus" :id="id">
    <div
      class="header"
      v-if="isShowTitle && !isTitlePosition"
     :style="{ textAlign: hanlin,fontFamily:tableTitleFamily,color:tableTitleColor,fontSize:tableTitleSize }"
    >
      <text style="margin-right: 10px">{{ number }}</text>
      <text>{{ name }}</text>
    </div>
    <div class="tableMain">
      <Paragraph :paragraphData="content[0]" :class="tableClassName" />
    </div>
   
    <div
      class="header"
      v-if="isShowTitle && isTitlePosition"
      :style="{ textAlign: hanlin,fontFamily:tableTitleFamily,color:tableTitleColor,fontSize:tableTitleSize }"
    >
      <text style="margin-right: 10px">{{ number }}</text>
      <text>{{ name }}</text>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from "vue";
import Paragraph from "../Pages/Paragraph.vue";
const props = defineProps({
  name: String,
  id: String,
  isShowTitle: Boolean,
  templateId: String,
  content: Array,
  isTitlePosition: Boolean,
  number: String,
  hanlin: String,
  tableTitleFamily:String,
  tableTitleSize:String,
  tableTitleColor:String
});
const tableClassName = computed(() => {
  if (!props.templateId) {
    return "__tables_default";
  } else {
    return `__tables_${props.templateId}`;
  }
});
</script>
