<style lang="less" scoped>
.video {
  width: 100%;
  height: 210px;
  .video-play {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}
.video-name {
  padding: 5px 0;
  text-align: center;
  font-size: 16px;

  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
</style>
<style lang="less" scoped>
// .el-collapse-item__header {
//   background-image: url(@/assets/images/readerResourcesIcon/modelBackground.png);
// }
// .el-collapse-item__content {
//   padding: 0;
// }
.collapse-main {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  margin-top: 16px;
  .left {
    display: flex;
    align-items: center;
    .icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
    
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      padding:0 10px;
      font-size: 16px;
      text-align: left;
    }
    .name {
      flex: 1;
      font-size: 16px;
      
      width: 100%;
      white-space: nowrap; /* 确保文本在一行内显示 */
      overflow: hidden; /* 超出容器部分的文本隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
    }
  }
  .right {
   
    cursor: pointer;
    font-size: 16px;
    min-width: 36px;
  }
}

.videoCss {
  width: 100%;
  background: #fff;
  display: flex;
  .video-play {
    width: 100%;
  }
}
</style>

<template>
  <div :id="id">
  
    <div
      class="collapse-main"
      @click="handleShow"
      :style="`background-image:url('${templateStyle.orderTemplateBgUrl || modelBackground}');background-size:100% 100%;width:100%; height:55px;`"
    >
      <!--  -->
      <div class="left">
        <div class="icon">
          <img :src="templateStyle.theme == 'dark' ?darkIcon: lightIcon " />
        </div>
        <div
          class="title"
          :style="{
            color: templateStyle.theme == 'dark' ? '#333' : '#fff',
          }"
        >
          视频
        </div>
        <div
          class="name"
          :style="{
            color: templateStyle.theme == 'dark' ? '#333' : '#fff',
          }"
        >
          <el-tooltip :content="videoTitle">{{ videoTitle }}</el-tooltip>
        </div>
      </div>

      <div
        class="right"
        :style="{
          color: templateStyle.theme == 'dark' ? '#333' : '#fff',
        }"
      >
        {{ show ? "收起" : "展开" }}
      </div>
    </div>

    <div
      v-show="show"
      class="videoCss"
      :theFirstFrame="theFirstFrame"
      :style="`width:100%;display:flex;justify-content: ${nodeAlign};`"
    >
      <!-- style="width: 100%; height: 100%; object-fit: fill" -->

      <video
        class="video-play"
        :src="src"
        preload="metadata"
        controls
        :style="`width:${width > 676 ? '100%' : width + 'px'};`"
        oncontextmenu="return false"
        crossorigin="anonymous"
        ref="videoRef"
        controlslist="nodownload"
        @canplay="loadeddata"
        @play="onVideoPlay"
        @pause="onVideoPause"
        @ended="onVideoEnded"
      >
        <source :src="src" type="video/mp4" />
        您的浏览器不支持视频播放。
      </video>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref } from "vue";
import html2canvas from "html2canvas";
import modelBackground from "@/assets/images/readerResourcesIcon/modelBackground.png";
import lightIcon from "@/assets/resources/light/video.svg";
import darkIcon from "@/assets/resources/dark/video.svg";
import useReader from "@/store/modules/reader";
import { updateVideoTime } from "@/api/book/reader";
const props = defineProps({
  height: String,
  width: String,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  videoTitle: String,
  id: String,
  isExpand: Boolean,
});
let videoRef = ref(null);
const show = ref(props.isExpand);
let theFirstFrame = ref(null);
const store = useReader();
const loading = ref(false);
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  obj.orderTemplateBgUrl = tmpStyle?.orderTemplateBgUrl;
  return obj;
});

const loadeddata = (e) => {
  if (props.checkTabs === "export") {
    setTimeout(async () => {
      const canvas = await html2canvas(videoRef.value);
      theFirstFrame.value = canvas.toDataURL("image/png");
    }, 100);
  }
};

const handleShow = () => {
  loading.value = true;
  requestAnimationFrame(() => {
    show.value = !show.value;
    if (show.value) {
      loading.value = false;
      videoRef.value.play();
    } else {
      videoRef.value.pause();
    }
  });
};

// 播放记录
const playRecord = ref({
  totalPlayTime: 0, // 总播放时长（秒）
  lastStartTime: 0, // 最近一次开始播放的时间
});

// 开始记录播放时长
const startRecordTime = () => {
  playRecord.value.lastStartTime = Date.now();
};

// 累计播放时长
const accumulatePlayTime = () => {
  if (playRecord.value.lastStartTime > 0) {
    const duration = Math.floor(
      (Date.now() - playRecord.value.lastStartTime) / 1000
    );
    playRecord.value.totalPlayTime += duration;
    playRecord.value.lastStartTime = 0;
  }
};
// 监听视频播放状态
const onVideoPlay = () => {
  startRecordTime();
};

const onVideoPause = () => {
  accumulatePlayTime();
};

// 监听视频结束事件 累计最后一段播放时长
const onVideoEnded = () => {
  accumulatePlayTime();
};

// 组件卸载
onBeforeUnmount(() => {
  console.log("Video.vue 卸载");
  if (playRecord.value.lastStartTime > 0) {
    accumulatePlayTime();
  }

  if (!store.videoPlayTime) {
    store.videoPlayTime = 0;
  }
  // 计算总时长
  store.videoPlayTime += playRecord.value.totalPlayTime;
});
</script>
