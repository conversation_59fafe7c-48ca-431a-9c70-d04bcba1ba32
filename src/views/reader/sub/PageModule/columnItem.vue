<template>
  <div :id="id" class="column-item" :style="`width:${width}%;flex-shrink: 0;${styleText} ${alignmentStyleText}`" >
    <slot> </slot>
  </div>
</template>
<script setup>
const { id } = defineProps({
  id: {
    default: null,
    type: String,
  },
  width: {
    default: null,
    type: Number,
  },
  styleText: {
    default: null,
    type: String,
  },
  alignmentStyleText: {
    default: null,
    type: String,
  },
});
</script>
<style lang="less" scoped>
.column-item{
  flex-shrink: 0;
  ::v-deep(.imageLayout){
    margin:0;
    
  }
 ::v-deep(.imageTitle){
    width: 80%;
    margin:0px auto 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: left;
    word-wrap: break-word;
  }
}


</style>
