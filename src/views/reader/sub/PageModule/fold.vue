<template>
  <div :id="id" ref="containerRef" class="umo-node-view">
    <div class="umo-node-view-extended-reading">
      <div
        :class="`extended-reading-template-bg ${isBg ? '__orderTemplateBgUrl__' : ''}`"
        style="min-height: 55px"
        @click="toggle"
      >
        <!-- :style="`background: url(${template?.orderTemplateBgUrl}) no-repeat center;height:54px;background-size:100% 100%;`" -->
        <div class="extended-left">
          <div
            class="extended-icon"
            v-if="iconUrl"
            :style="`transform: rotate(${toggleVisible ? '90deg' : '0deg'})`"
          >

          <FoldIcon1 v-if="iconUrl === 'foldIcon1'" :color="iconColor" class="beforeIcon" />
          <FoldIcon2 v-else-if="iconUrl === 'foldIcon2'" :color="iconColor" />
          <FoldIcon3 v-else-if="iconUrl === 'foldIcon3'" :color="iconColor" />
          <FoldIcon4 v-else-if="iconUrl === 'foldIcon4'" :color="iconColor" />
          <FoldIcon5 v-else-if="iconUrl === 'foldIcon5'" :color="iconColor" />
          <FoldIcon6 v-else-if="iconUrl === 'foldIcon6'" :color="iconColor" />
            <img v-else :src="iconUrl" alt="" />
        

        
          </div>
        
          <el-tooltip effect="dark" :content="title" placement="top">
            <div class="extended-name" :style="titleCss">
              {{ title }}
            </div>
          </el-tooltip>
        </div>
        <div class="extended-right" :style="titleCss">
          {{ toggleVisible ?  collapseText: expandText}}
        </div>
      </div>

      <div v-if="toggleVisible" class="extended-reading-template-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { computed, ref } from "vue";
import FoldIcon1 from "./bubbleIconTemplate/flodIcon1.vue";
import FoldIcon2 from "./bubbleIconTemplate/flodIcon2.vue";
import FoldIcon3 from "./bubbleIconTemplate/flodIcon3.vue";
import FoldIcon4 from "./bubbleIconTemplate/flodIcon4.vue";
import FoldIcon5 from "./bubbleIconTemplate/flodIcon5.vue";
import FoldIcon6 from "./bubbleIconTemplate/flodIcon6.vue";
const props = defineProps({
  id: String,
  iconUrl: String,
  title: String,
  isOpen: Boolean,
  fontColor: String,
  fontSize: [String, Number],
  isStrong: Boolean,
  isBg: Boolean,
  fontFamily:String,
  expandText:String,
  collapseText:String,
  iconColor:String
});
// const { pageTemplateId } = useTemplate();
// const template = pageTemplateId.value;
const toggleVisible = ref(props.isOpen);
const toggle = () => {
  toggleVisible.value = !toggleVisible.value;
};

const titleCss = computed(() => {
  return {
    color: props.fontColor,
    fontSize: `${props.fontSize}px`,
    fontWeight: props.isStrong ? "bold" : "normal",
    fontFamily: props.fontFamily
  };
});
</script>

<style lang="scss" scoped>
.umo-node-view {
  background-color: transparent;
  margin: 20px 0;
}
.umo-node-view-extended-reading {
  *{
    margin:0;
  }
  position: relative;
  cursor: pointer;
  width: 100%;
  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .extended-left {
      display: flex;
      align-items: center;
      .extended-icon {
        width: 24px;
        height: 24px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: #333;
        margin-left: 10px;
        width: 450px;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
      margin-right:3px;
      cursor: pointer;
    }
  }
  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;

    width: 100%;
    // min-height: 100px;
    box-sizing: border-box;
    // font-size: 18px;
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
    overflow: hidden;
    // line-height: 1.6;
    ::v-deep(p){
      margin:0;
    }
  }
}
</style>
