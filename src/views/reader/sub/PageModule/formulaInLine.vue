<!-- 模块说明 -->
<style lang="scss" scoped>
.ImageIcon {
  display: inline-flex;
  position: relative;
  top: 5px;
  .el-image {
    display: inline-flex;
    /* overflow: hidden; */
    position: relative;
  }
}

.imageContent {
  display: flex;
  justify-content: center;
  align-items: center;
}
.imageText {
  padding: 20px 0;
  text-align: center;
}
</style>

<template>
  <div
    :id="id"
    class="ImageIcon"
    @click="openVisible = true"
    :style="`float:${nodeFloat}`"
  >
    <el-image
      :src="src"
      :style="{ width: width + 'px', height: height + 'px' }"
       :preview-src-list="[src]"
    />
  </div>

  <!-- <el-dialog v-model="openVisible" title="公式预览" width="30%" center>
    <div class="imageContent">
      <img :src="src" fit="cover" style="max-width: 100%" />
    </div>

    <div class="imageText" :style="{ color: linkAddress ? '#409EFF' : '' }">
      <a :href="linkAddress" target="_blank" v-if="linkAddress">
        {{ imageTitle }}</a
      >
      <span v-else> {{ imageTitle }}</span>
    </div>
  </el-dialog> -->
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  src: String,
  id: String,
  previewtype: String,
  width: Number,
  name: String,
  height: Number,
  linkAddress: String,
  imageTitle: String,
  nodeFloat: String,
});

const openVisible = ref(false);
</script>
