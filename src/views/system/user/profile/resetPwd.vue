<template>
   <el-form ref="pwdRef" :model="user" :rules="rules" label-width="80px">
      <el-form-item label="旧密码" prop="oldPassword">
         <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
         <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
         <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
      </el-form-item>
      <div style="text-align: center;">
        <el-button  @click="close" style="width: 80px;">取消</el-button>
        <el-button type="primary" @click="submit" style="width: 80px;">确认修改</el-button>
      </div>
   </el-form>
</template>

<script setup>
import { updateUserPwd } from "@/api/system/user";
import useUserStore from '@/store/modules/user';
import { ElNotification } from "element-plus";
const emit = defineEmits(['close'])
const userStore = useUserStore()
const { proxy } = getCurrentInstance();

const user = reactive({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined
});

const equalToPassword = (rule, value, callback) => {
  if (user.newPassword !== value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};
const validateInput = (inputValue) => {
  const value = inputValue.trim(); // 去除前后空格
  const lengthValid = value.length >= 8 && value.length <= 14;
  const noChineseOrSpace = !/[一-龥\s]/.test(value); // 不允许中文和空格
  const hasDigit = /\d/.test(value);
  const hasLetter = /[a-zA-Z]/.test(value);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

  const typesCount = [hasDigit, hasLetter, hasSpecialChar].filter(Boolean).length;

  if (!lengthValid) {
    ElNotification({
      title: '操作提示',
      message: '请输入8-14位密码',
      type: 'error',
    });
    return false;
  } else if (!noChineseOrSpace) {
    ElNotification({
      title: '操作提示',
      message: '不允许输入中文和空格',
      type: 'error',
    });
    return false;
  } else if (typesCount < 2) {
    ElNotification({
      title: '操作提示',
      message: '至少包含数字、字母和特殊符号中的两种组合',
      type: 'error',
    });
    return false;
  }
  return true;
};
const rules = ref({
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  newPassword: [{ required: true, message: "新密码不能为空", trigger: "blur" }, { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }, { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }],
  confirmPassword: [{ required: true, message: "确认密码不能为空", trigger: "blur" }, { required: true, validator: equalToPassword, trigger: "blur" }]
});

/** 提交按钮 */
function submit() {
  if (!validateInput(user.newPassword)) return;
  proxy.$refs.pwdRef.validate(valid => {
    if (valid) {
      updateUserPwd(user.oldPassword, user.newPassword).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        proxy.$tab.closePage();
        userStore.logOut().then(() => {
          location.href = '/index' // 注销后跳转到首页
        })
      });
    }
  });
};

/** 关闭按钮 */
function close() {
  proxy.$refs.pwdRef.resetFields();
  emit('close');
};
</script>
