<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
        >
          <el-menu-item index="1">基础信息</el-menu-item>
        </el-menu>
      </div>
    </div>
    <div>
      <el-row>
        <el-col :span="2" style="text-align: end">
          <el-avatar
            :size="40"
            :src="formData.avatar ? formData.avatar : defaultAvatar"
          />
        </el-col>
        <el-col :span="17" style="margin: 10px">
          <el-text>{{ formData.displayNickName }}</el-text>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" plain @click="edit">编辑</el-button>
        </el-col>
        <el-col :span="2">
          <!-- 上传头像按钮，点击时打开头像选择对话框 -->
          <el-button type="primary" plain @click="openAvatarDialog"
            >上传头像</el-button
          >
        </el-col>
      </el-row>
      <el-row style="margin: 20px">
        <el-form label-position="right" label-width="100px">
          <el-form-item label="手机号:">
            <el-text>{{ formData.phonenumber }}</el-text>
          </el-form-item>
          <el-form-item label="真实姓名:" v-if="isStudentOrTeacher">
            <el-text>{{ formData.displayRealName }}</el-text>
          </el-form-item>
          <el-form-item label="学校:" v-if="isStudentOrTeacher">
            <el-text>{{ formData.displaySchoolName }}</el-text>
          </el-form-item>
          <el-form-item label="院系:" v-if="isStudentOrTeacher">
            <el-text>{{ formData.displayAcademyName }}</el-text>
          </el-form-item>
          <el-form-item label="专业:" v-if="isStudentOrTeacher">
            <el-text>{{ formData.displaySpecialityName }}</el-text>
          </el-form-item>
          <el-form-item label="邮箱:">
            <el-text>{{ formData.displayEmail }}</el-text>
            <!-- <el-button v-else type="text" @click="bindEmail()">绑定邮箱</el-button> -->
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="margin: 20px; text-align: end; margin-right: 46px">
        <el-col :span="24">
          <el-button type="primary" plain @click="logout()">退出登录</el-button>
        </el-col>
      </el-row>
    </div>
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
        >
          <el-menu-item index="1">账号安全</el-menu-item>
        </el-menu>
      </div>
    </div>
    <div>
      <el-row>
        <el-col :span="2" style="text-align: end; line-height: 48px">
          <el-icon>
            <Lock />
          </el-icon>
        </el-col>
        <el-col :span="19" style="margin: 10px">
          <el-text>修改密码</el-text>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" plain @click="openChangePasswordDialog"
            >修改密码</el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="2" style="text-align: end; line-height: 48px">
          <el-icon>
            <Lock />
          </el-icon>
        </el-col>
        <el-col :span="19" style="margin: 10px">
          <el-text
            >注销账号<br />注销后账号所有数据将被销毁并不可找回，请谨慎操作。</el-text
          >
        </el-col>
        <el-col :span="2">
          <el-button type="primary" plain @click="deleteAccount"
            >注销账号</el-button
          >
        </el-col>
      </el-row>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" title="编辑信息" width="30%">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="昵称:" prop="nickName">
          <el-input
            v-model="formData.nickName"
            placeholder="请输入昵称"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号:">
          <span> {{ formData.phonenumber }}</span>
        </el-form-item>
        <el-form-item label="真实姓名:" v-if="isStudentOrTeacher" >
            <!-- <el-input v-if="formData.displayRealName == '' || formData.displayRealName == null" v-model="formData.realName" placeholder="请输入真实姓名" />
            <span v-else >{{formData.realName}}</span> -->
            <span>{{formData.realName}}</span>
          </el-form-item>
        <el-form-item label="学校/院系/专业" prop="schoolDepartmentMajor">
          <span  v-if="modifyCount >= 3">
            {{ formData.schoolName }}
            {{ formData.academyId? "/" + formData.academyName : "" }}
            {{
              formData.specialityId ? "/" + formData.specialityName : ""
            }}
          </span>
          <el-cascader
            v-else
            v-model="formData.schoolDepartmentMajor"
            :options="cascaderOptions"
            :props="{
              value: 'id',
              label: 'schoolName',
              children: 'children',
            }"
            value-key="id"
            placeholder="请选择您的学校/院系/专业"
            style="width: 100%"
            popper-style="min-width: 600px;"
            @change="handleCascaderChange"
            :disabled="formData.isReadOnly"
            :change-on-select="false"
          ></el-cascader>
        </el-form-item>
        <!-- <el-form-item label="学校:"  prop="schoolName">
          <span v-if="isStudentOrTeacher"> {{formData.schoolName}}</span>
          <el-select v-else v-model="formData.schoolName" placeholder="请选择学校" size="large" style="width: 240px" @change="handleSchoolChange" >
            <el-option v-for="item in schoolList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId" placeholder="请选择学校" />
          </el-select>
        </el-form-item>
        <el-form-item label="院系:"  prop="academyName">
          <span v-if="isStudentOrTeacher"> {{formData.academyName}}</span>
          <el-select v-else v-model="formData.academyName" placeholder="请选择院系" size="large" style="width: 240px" @change="handleAcademyChange">
            <el-option v-for="item in departmentsList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId" placeholder="请选择学校" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="邮箱:" prop="email">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-alert
            :title="'一个账号只有3次修改学校机会，您已修改' + modifyCount + '/3次！若3次修改次数用尽，请您联系本校老师进行信息修改。'"
            type="warning"
            show-icon
            :closable="false"
            style="margin-top: 10px"
          />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveChanges">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="30%">
      <resetPwd @close="passwordDialogVisible = false" />
    </el-dialog>

    <!-- 头像选择对话框 -->
    <el-dialog v-model="avatarDialogVisible" title="个人头像设置" width="30%"  @close="cancelAvatarSelection">
      <template #title>
        <div class="dialog-title">
          个人头像设置
        </div>
      </template>
      <el-alert
        title="点击选择头像，选择后点击确定"
        type="warning"
        :closable="false"
        class="mb-4"
      />

      <!-- 头像选择区域 -->
      <div class="avatar-container">
        <el-row :gutter="20">
          <el-col v-for="(avatar, index) in avatarList" :key="index"  :span="4.8" class="avatar-col" style="border-radius: 50%;text-align: -webkit-center !important;">
            <div
              class="avatar-item"
              :class="{
                'selected-avatar': avatar.avatarUrl === selectedAvatar,
              }"
              @click="selectAvatar(avatar.avatarUrl)"
            >
              <el-avatar :size="60" :src="avatar.avatarUrl" class="mx-auto" />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页组件 -->
      <!-- <el-pagination
        small
        layout="prev, pager, next"
        :total="avatarTotal"
        :page-size="pageSize"
        class="mt-4 justify-center"
        @current-change="handlePageChange"
      /> -->

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAvatarSelection">取消</el-button>
          <el-button type="primary" @click="confirmAvatarSelection"
            >确定选择</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
  <cancelAccountDialog :form="formData" v-model="cancelAccountDialogVisible" />
</template>

<script setup>
import { onMounted, reactive, ref, computed } from "vue";
import {
  ElNotification,
  ElForm,
  ElFormItem,
  ElText,
  ElDialog,
  ElInput,
  ElButton,
  ElMessageBox,
  ElMessage,
  ElAvatar,
  ElRow,
  ElCol,
} from "element-plus";
import { useRoute } from "vue-router";
import useUserStore from "@/store/modules/user";
import { getUser, updateUser, cancelAccount } from "@/api/edu/dutpUser";
import { listAvatar } from "@/api/system/avatar";
import { listAllSchool } from "@/api/basic/school";
import resetPwd from "@/views/system/user/profile/resetPwd.vue";
import defaultAvatar from "@/assets/images/home/<USER>";
import { listSchool } from "@/api/basic/school";
import cancelAccountDialog from "./cancelAccountDialog.vue";
const modifyCount = ref(0);
const cancelAccountDialogVisible = ref(false);
const schoolQueryParams = {
  pageNum: 1,
  pageSize: 9999,
  schoolName: null,
  schoolCode: null,
};
const route = useRoute();
const userStore = useUserStore();
const userId = route.params.userId;
const activeIndex = ref("1");
// 头像选择相关
const avatarDialogVisible = ref(false);
const avatarList = ref([]); // 头像列表
const selectedAvatar = ref("");
const schoolList = ref([]);
const departmentsList = ref([]);
const cascaderOptions = ref([]);
// 表单数据
const formData = reactive({
  avatar: "",
  userId: null,
  nickName: null,
  realName: null,
  displayRealName: null,
  displayNickName: null,
  userType: null,
  phonenumber: null,
  schoolName: null,
  displaySchoolName: null,
  schoolId: null,
  academyId: null,
  academyName: null,
  displayAcademyName: null,
  specialityId: null,
  specialityName: null,
  displaySpecialityName: null,
  email: null,
  displayEmail: null,
});
// 定义表单引用
const formRef = ref();

// 定义校验规则
const rules = reactive({
  nickName: [{ required: true, message: "请填写昵称", trigger: "change" }],
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    {
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: ["blur", "change"],
    },
  ],
});
// 控制编辑框的显示
const dialogVisible = ref(false);
// 计算属性：判断用户是否是普通读者，允许编辑所有信息（除了手机号）
const canEditInfo = computed(() => formData.userType === "0");
// 计算属性：判断是否是学生或教师
const isStudentOrTeacher = computed(
  () => formData.userType === "1" || formData.userType === "2"
);
// 修改密码对话框的显示控制
const passwordDialogVisible = ref(false);

// 修改密码
const openChangePasswordDialog = () => {
  passwordDialogVisible.value = true;
};
// 编辑按钮点击时打开编辑框
const edit = () => {
  dialogVisible.value = true;
};

// 获取用户信息
const getUserInfo = async () => {
  await getUser().then((res) => {
    if (res.code === 200) {
      formData.avatar = res.data.avatar;
      selectedAvatar.value = res.data.avatar;
      formData.userId = res.data.userId;
      formData.userType = res.data.userType;
      formData.nickName = res.data.nickName;
      formData.realName = res.data.realName;
      formData.displayRealName = res.data.realName;
      formData.displayNickName = res.data.nickName;
      formData.phonenumber = res.data.phonenumber;
      formData.schoolId = res.data.schoolId;
      formData.schoolName = res.data.schoolName;
      formData.displaySchoolName = res.data.schoolName;
      formData.academyId = res.data.academyId;
      formData.academyName = res.data.academyName;
      formData.displayAcademyName = res.data.academyName;
      formData.specialityId = res.data.specialityId;
      formData.specialityName = res.data.specialityName;
      formData.displaySpecialityName = res.data.specialityName;
      formData.schoolDepartmentMajor = [
      res.data.schoolId,
      res.data.academyId,
      res.data.specialityId,
      ].filter((id) => id != null);
      listAllSchool().then((response) => {
        schoolList.value = response.rows.filter((item) => item.dataType === 0);
        departmentsList.value = response.rows.filter(
          (item) => item.dataType === 1
        );
        const findList = response.rows.find(
          (school) => school.schoolId === res.data.academyId
        );
        formData.academyName = findList ? findList.schoolName : "";
      });
      formData.email = res.data.email;
      formData.displayEmail = res.data.email;
      modifyCount.value = res.data.modifyCount === null ? 0 : res.data.modifyCount;
    }
  });
};
const handleSchoolChange = (schoolId) => {
  const selectedSchool = findSchoolById(schoolId);
  if (selectedSchool) {
    formData.schoolName = selectedSchool.schoolName;
    formData.schoolId = selectedSchool.schoolId;
  } else {
    formData.schoolName = ""; // 或者设置一个默认值
    formData.schoolId = "";
  }
};
const findSchoolById = (schoolId) => {
  return schoolList.value.find((school) => school.schoolId === schoolId);
};
const handleAcademyChange = (schoolId) => {
  const selectedSchool = findAcademyById(schoolId);
  if (selectedSchool) {
    formData.academyName = selectedSchool.schoolName;
    formData.academyId = selectedSchool.schoolId;
  } else {
    formData.academyName = ""; // 或者设置一个默认值
    formData.academyId = "";
  }
};
const findAcademyById = (schoolId) => {
  return departmentsList.value.find((school) => school.schoolId === schoolId);
};

// 打开头像选择框
const openAvatarDialog = () => {
  avatarDialogVisible.value = true;
};

// 选择头像
const selectAvatar = (avatar) => {
  selectedAvatar.value = avatar;
};

// 确认选择头像
const confirmAvatarSelection = () => {
  formData.avatar = selectedAvatar.value;
  avatarDialogVisible.value = false;

  // 如果头像改变，更新数据库中的头像
  updateUser(formData).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: "头像更新成功",
        type: "success",
      });
    } else {
      ElNotification({
        title: "头像更新失败",
        type: "error",
      });
    }
  });
};

// 保存编辑信息
const saveChanges = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      formData.schoolId = formData.schoolDepartmentMajor[0];
      formData.academyId = formData.schoolDepartmentMajor[1]
        ? formData.schoolDepartmentMajor[1]
        : null;
      formData.specialityId = formData.schoolDepartmentMajor[2]
        ? formData.schoolDepartmentMajor[2]
        : null;
      updateUser(formData).then((res) => {
        if (res.code === 200) {
          ElMessage.warning("修改成功，3秒后将自动退出登录！");
          getUserInfo();
          
          // 3秒后退出登录
          setTimeout(() => {
            userStore.logOut().then(() => {
              location.href = "/index"; // 退出后跳转到首页
            });
          }, 3000);
        } else {
          ElNotification({
            title: "修改失败",
            type: "error",
          });
        }
      });
      dialogVisible.value = false;
    }
  });
};
const getSchoolList = () => {
  listSchool(schoolQueryParams).then((response) => {
    cascaderOptions.value = response.data;
    const res = cascaderOptions.value.filter((item) => item.id === formData.schoolDepartmentMajor[0]);
    console.log("id",  formData.schoolDepartmentMajor[0]);
    console.log("res",  res);
    console.log("cascaderOptions", cascaderOptions.value );
  });
};
// 绑定邮箱
const bindEmail = () => {};

// 注销功能
const logout = () => {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/index"; // 注销后跳转到首页
      });
    })
    .catch(() => {});
};

// 注销账号
const deleteAccount = () => {
  ElMessageBox.confirm(
    "确定要注销账号吗？注销后账号所有数据将被销毁并不可找回，请谨慎操作。",
    "提示",
    {
      confirmButtonText: "继续注销",
      cancelButtonText: "取消",
      type: "error",
    }
  )
    .then(() => {
      cancelAccountDialogVisible.value = true;
      // cancelAccount([formData.userId]).then((res) => {
      //   if (res.code === 200) {
      //     ElNotification({
      //       title: '注销成功',
      //       type: 'success',
      //     })
      //     userStore.logOut().then(() => {
      //       location.href = '/index' // 注销后跳转到首页
      //     })
      //   } else {
      //     ElNotification({
      //       title: '注销失败',
      //       type: 'error',
      //     })
      //   }
      // })
    })
    .catch(() => {});
};
// 分页相关
const pageSize = ref(99);
const avatarTotal = ref(0);
const currentPage = ref(1);

// 获取头像列表（带分页）
const getAvatarList = () => {
  const parm = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  };
  listAvatar(parm).then((res) => {
    avatarList.value = res.rows;
    avatarTotal.value = res.total;
  });
};

// 分页切换
const handlePageChange = (page) => {
  currentPage.value = page;
  getAvatarList();
};

// 取消选择
const cancelAvatarSelection = () => {
  avatarDialogVisible.value = false;
  selectedAvatar.value = formData.avatar;
};
// 页面加载时获取用户信息
onMounted(async () => {
  await getUserInfo();
  // 获取头像列表
  getAvatarList();
  getSchoolList();
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -90px;
  .right-top-con {
    width: 100%;
    border-bottom: 1px solid #e5e6e7;
    margin-bottom: 10px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .menu-con {
      margin-left: 57px;
      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }
    .filter-con {
      margin-right: 61px;
    }
  }
}
.avatar-col {
  border-radius: 50%; // 改为圆形

  max-width: 20%; // 5列布局
  flex: 0 0 20%;  // 弹性布局保持比例
}
.avatar-container {
  .el-row {
    flex-wrap: wrap; // 允许换行
    margin-left: -4px !important;
    margin-right: -4px !important;
    
    .el-col {
      padding-left: 4px !important;
      padding-right: 4px !important;
      margin-bottom: 8px;
    }
  }
  border: 1px solid #ebeef5; /* 轻微边框 */
  padding: 10px;
  background-color: #fafafa; /* 背景颜色 */
}

.avatar-item {
  border-radius: 50% !important; // 改为圆形
  text-align: -webkit-center !important;
  width: 80px;       // 添加固定宽度
  height: 80px;      // 添加固定高度
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
  }
}

.selected-avatar {
  border: 2px solid #409eff;
  background-color: rgba(64,158,255,0.1);
  position: relative;
  
  &::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #409eff;
    border-radius: 50%;
  }
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

.justify-center {
  justify-content: center;
}
.dialog-title {
  text-align: center; /* 标题居中 */
  font-size: 18px; /* 调整字体大小 */
  font-weight: bold; /* 加粗字体 */
  color: #303133; /* 调整字体颜色 */
}
</style>
